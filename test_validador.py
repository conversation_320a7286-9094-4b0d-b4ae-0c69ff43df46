#!/usr/bin/env python3
"""
Script de prueba para el validador SAT web.
Prueba con una sola URL antes de procesar todas.
"""

from validador_sat_simple import ValidadorSATSimple
import json

def test_una_url():
    """Prueba el validador con una sola URL."""
    print("=== PRUEBA DEL VALIDADOR SAT WEB ===\n")
    
    # URL de prueba
    url_test = "https://siat.sat.gob.mx/app/qr/faces/pages/mobile/validadorqr.jsf?D1=10&D2=1&D3=17060027240_AEMN940308PF4"
    
    # Crear validador
    validador = ValidadorSATSimple()

    # Consultar URL
    print("Consultando URL de prueba...")
    resultado = validador.consultar_url_sat(url_test)

    # Mostrar resultado
    print("\nRESULTADO:")
    print("-" * 40)
    print(json.dumps(resultado, indent=2, ensure_ascii=False))

    # Generar archivo de prueba
    if resultado.get('pagina_valida'):
        print("\n✓ Prueba exitosa! Generando archivos de prueba...")
        validador.generar_csv_excel([resultado], "prueba_validacion.csv")
        validador.generar_reporte_texto([resultado], "prueba_reporte.txt")
        print("Archivos generados: prueba_validacion.csv, prueba_reporte.txt")
    else:
        print("\n✗ La prueba falló. Revisa la conexión o la URL.")

if __name__ == "__main__":
    test_una_url()
