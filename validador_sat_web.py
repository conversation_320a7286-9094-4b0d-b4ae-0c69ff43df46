#!/usr/bin/env python3
"""
Sistema para validar URLs de códigos QR del SAT y extraer información detallada.
Accede a las páginas web del SAT y extrae datos estructurados para Excel.
"""

import os
import time
import json
import pandas as pd
from datetime import datetime
from pathlib import Path
import re
from typing import Dict, List, Optional

try:
    import requests
    from bs4 import BeautifulSoup
    import urllib3
    from urllib.parse import urlparse, parse_qs
except ImportError as e:
    print(f"Error: Instala las dependencias con:")
    print(f"pip install requests beautifulsoup4 pandas openpyxl urllib3")
    print(f"Dependencia faltante: {e}")
    exit(1)

# Deshabilitar advertencias SSL para el SAT
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class ValidadorSATWeb:
    def __init__(self):
        """Inicializa el validador web del SAT."""
        self.session = requests.Session()
        self.session.verify = False  # El SAT a veces tiene problemas con SSL
        
        # Headers para simular un navegador real
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'es-MX,es;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        self.resultados = []
        self.delay_entre_requests = 2  # Segundos entre requests para no sobrecargar el servidor
    
    def extraer_informacion_pagina(self, html_content: str, url: str) -> Dict:
        """
        Extrae información estructurada de la página HTML del SAT.
        
        Args:
            html_content: Contenido HTML de la página
            url: URL original consultada
            
        Returns:
            Diccionario con información extraída
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        
        info = {
            'url_consultada': url,
            'timestamp_consulta': datetime.now().isoformat(),
            'pagina_valida': False,
            'error': None,
            
            # Datos de identificación
            'rfc': None,
            'curp': None,
            'nombre': None,
            'apellido_paterno': None,
            'apellido_materno': None,
            'nombre_completo': None,
            'fecha_nacimiento': None,
            'fecha_inicio_operaciones': None,
            'situacion_contribuyente': None,
            'fecha_ultimo_cambio_situacion': None,
            
            # Datos de ubicación
            'entidad_federativa': None,
            'municipio': None,
            'colonia': None,
            'tipo_vialidad': None,
            'nombre_vialidad': None,
            'numero_exterior': None,
            'numero_interior': None,
            'codigo_postal': None,
            'correo_electronico': None,
            'administracion_local': None,
            
            # Características fiscales
            'regimen_fiscal': None,
            'fecha_alta_regimen': None
        }
        
        try:
            # Verificar que la página sea válida (contiene información del RFC)
            rfc_text = soup.find(text=re.compile(r'El RFC: .+, tiene asociada la siguiente información'))
            if rfc_text:
                info['pagina_valida'] = True
                # Extraer RFC del texto
                rfc_match = re.search(r'El RFC: ([A-Z0-9]+),', rfc_text)
                if rfc_match:
                    info['rfc'] = rfc_match.group(1)
            
            # Extraer información de las tablas
            # Buscar todas las filas de datos
            rows = soup.find_all('row')
            
            for row in rows:
                row_text = row.get_text(strip=True)
                
                # Datos de identificación
                if 'CURP:' in row_text:
                    info['curp'] = self._extraer_valor_campo(row_text, 'CURP:')
                elif 'Nombre:' in row_text:
                    info['nombre'] = self._extraer_valor_campo(row_text, 'Nombre:')
                elif 'Apellido Paterno:' in row_text:
                    info['apellido_paterno'] = self._extraer_valor_campo(row_text, 'Apellido Paterno:')
                elif 'Apellido Materno:' in row_text:
                    info['apellido_materno'] = self._extraer_valor_campo(row_text, 'Apellido Materno:')
                elif 'Fecha Nacimiento:' in row_text:
                    info['fecha_nacimiento'] = self._extraer_valor_campo(row_text, 'Fecha Nacimiento:')
                elif 'Fecha de Inicio de operaciones:' in row_text:
                    info['fecha_inicio_operaciones'] = self._extraer_valor_campo(row_text, 'Fecha de Inicio de operaciones:')
                elif 'Situación del contribuyente:' in row_text:
                    info['situacion_contribuyente'] = self._extraer_valor_campo(row_text, 'Situación del contribuyente:')
                elif 'Fecha del último cambio de situación:' in row_text:
                    info['fecha_ultimo_cambio_situacion'] = self._extraer_valor_campo(row_text, 'Fecha del último cambio de situación:')
                
                # Datos de ubicación
                elif 'Entidad Federativa:' in row_text:
                    info['entidad_federativa'] = self._extraer_valor_campo(row_text, 'Entidad Federativa:')
                elif 'Municipio o delegación:' in row_text:
                    info['municipio'] = self._extraer_valor_campo(row_text, 'Municipio o delegación:')
                elif 'Colonia:' in row_text:
                    info['colonia'] = self._extraer_valor_campo(row_text, 'Colonia:')
                elif 'Tipo de vialidad:' in row_text:
                    info['tipo_vialidad'] = self._extraer_valor_campo(row_text, 'Tipo de vialidad:')
                elif 'Nombre de la vialidad:' in row_text:
                    info['nombre_vialidad'] = self._extraer_valor_campo(row_text, 'Nombre de la vialidad:')
                elif 'Número exterior:' in row_text:
                    info['numero_exterior'] = self._extraer_valor_campo(row_text, 'Número exterior:')
                elif 'Número interior:' in row_text:
                    info['numero_interior'] = self._extraer_valor_campo(row_text, 'Número interior:')
                elif 'CP:' in row_text:
                    info['codigo_postal'] = self._extraer_valor_campo(row_text, 'CP:')
                elif 'Correo electrónico:' in row_text:
                    info['correo_electronico'] = self._extraer_valor_campo(row_text, 'Correo electrónico:')
                elif 'AL:' in row_text:
                    info['administracion_local'] = self._extraer_valor_campo(row_text, 'AL:')
                
                # Características fiscales
                elif 'Régimen:' in row_text:
                    info['regimen_fiscal'] = self._extraer_valor_campo(row_text, 'Régimen:')
                elif 'Fecha de alta:' in row_text:
                    info['fecha_alta_regimen'] = self._extraer_valor_campo(row_text, 'Fecha de alta:')
            
            # Construir nombre completo
            partes_nombre = [info.get('nombre'), info.get('apellido_paterno'), info.get('apellido_materno')]
            info['nombre_completo'] = ' '.join([parte for parte in partes_nombre if parte])
            
        except Exception as e:
            info['error'] = str(e)
            print(f"Error procesando HTML: {e}")
        
        return info
    
    def _extraer_valor_campo(self, texto: str, campo: str) -> Optional[str]:
        """
        Extrae el valor de un campo específico del texto.
        
        Args:
            texto: Texto completo de la fila
            campo: Nombre del campo a extraer
            
        Returns:
            Valor del campo o None si no se encuentra
        """
        try:
            # El formato es generalmente "Campo: Valor"
            if campo in texto:
                partes = texto.split(campo, 1)
                if len(partes) > 1:
                    valor = partes[1].strip()
                    return valor if valor else None
        except Exception:
            pass
        return None
    
    def consultar_url_sat(self, url: str) -> Dict:
        """
        Consulta una URL del SAT y extrae la información.
        
        Args:
            url: URL del código QR del SAT
            
        Returns:
            Diccionario con información extraída
        """
        print(f"Consultando: {url}")
        
        try:
            # Realizar request con timeout
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # Extraer información de la página
            info = self.extraer_informacion_pagina(response.text, url)
            
            if info['pagina_valida']:
                print(f"  ✓ Información extraída para RFC: {info.get('rfc', 'N/A')}")
                print(f"    Nombre: {info.get('nombre_completo', 'N/A')}")
                print(f"    Situación: {info.get('situacion_contribuyente', 'N/A')}")
            else:
                print(f"  ⚠ Página no válida o sin información")
            
            return info
            
        except requests.exceptions.RequestException as e:
            print(f"  ✗ Error de conexión: {e}")
            return {
                'url_consultada': url,
                'timestamp_consulta': datetime.now().isoformat(),
                'pagina_valida': False,
                'error': f"Error de conexión: {str(e)}"
            }
        except Exception as e:
            print(f"  ✗ Error general: {e}")
            return {
                'url_consultada': url,
                'timestamp_consulta': datetime.now().isoformat(),
                'pagina_valida': False,
                'error': f"Error general: {str(e)}"
            }
    
    def procesar_urls_desde_archivo(self, archivo_qr: str) -> List[Dict]:
        """
        Procesa URLs desde un archivo de QR extraídos.
        
        Args:
            archivo_qr: Ruta al archivo con URLs de QR
            
        Returns:
            Lista de diccionarios con información procesada
        """
        urls_encontradas = []
        
        try:
            with open(archivo_qr, 'r', encoding='utf-8') as f:
                contenido = f.read()
            
            # Buscar URLs en el contenido
            patron_url = r'(https://siat\.sat\.gob\.mx/app/qr/faces/pages/mobile/validadorqr\.jsf[^\s]+)'
            urls = re.findall(patron_url, contenido)
            
            print(f"Encontradas {len(urls)} URLs para procesar")
            
            for i, url in enumerate(urls, 1):
                print(f"\nProcesando {i}/{len(urls)}")
                
                # Consultar URL
                info = self.consultar_url_sat(url)
                urls_encontradas.append(info)
                
                # Delay entre requests para no sobrecargar el servidor
                if i < len(urls):
                    print(f"  Esperando {self.delay_entre_requests} segundos...")
                    time.sleep(self.delay_entre_requests)
            
        except Exception as e:
            print(f"Error procesando archivo: {e}")
        
        return urls_encontradas

    def generar_excel_detallado(self, datos: List[Dict], archivo_salida: str = "validacion_sat_detallada.xlsx"):
        """
        Genera un archivo Excel con la información detallada extraída.

        Args:
            datos: Lista de diccionarios con información extraída
            archivo_salida: Nombre del archivo Excel de salida
        """
        try:
            # Crear DataFrame con todos los datos
            df = pd.DataFrame(datos)

            # Reordenar columnas para mejor presentación
            columnas_ordenadas = [
                'rfc', 'nombre_completo', 'nombre', 'apellido_paterno', 'apellido_materno',
                'curp', 'fecha_nacimiento', 'situacion_contribuyente',
                'fecha_inicio_operaciones', 'fecha_ultimo_cambio_situacion',
                'entidad_federativa', 'municipio', 'colonia', 'codigo_postal',
                'tipo_vialidad', 'nombre_vialidad', 'numero_exterior', 'numero_interior',
                'correo_electronico', 'administracion_local',
                'regimen_fiscal', 'fecha_alta_regimen',
                'pagina_valida', 'url_consultada', 'timestamp_consulta', 'error'
            ]

            # Filtrar solo las columnas que existen en el DataFrame
            columnas_existentes = [col for col in columnas_ordenadas if col in df.columns]
            df_ordenado = df[columnas_existentes]

            # Crear archivo Excel con formato
            with pd.ExcelWriter(archivo_salida, engine='openpyxl') as writer:
                # Hoja principal con todos los datos
                df_ordenado.to_excel(writer, sheet_name='Datos Completos', index=False)

                # Hoja de resumen solo con datos válidos
                df_validos = df_ordenado[df_ordenado['pagina_valida'] == True]
                if not df_validos.empty:
                    columnas_resumen = [
                        'rfc', 'nombre_completo', 'situacion_contribuyente',
                        'entidad_federativa', 'municipio', 'codigo_postal',
                        'regimen_fiscal', 'correo_electronico'
                    ]
                    columnas_resumen_existentes = [col for col in columnas_resumen if col in df_validos.columns]
                    df_resumen = df_validos[columnas_resumen_existentes]
                    df_resumen.to_excel(writer, sheet_name='Resumen', index=False)

                # Hoja de estadísticas
                stats = self._generar_estadisticas(df)
                df_stats = pd.DataFrame(list(stats.items()), columns=['Estadística', 'Valor'])
                df_stats.to_excel(writer, sheet_name='Estadísticas', index=False)

                # Formatear hojas
                self._formatear_excel(writer, df_ordenado)

            print(f"✓ Archivo Excel generado: {archivo_salida}")

        except Exception as e:
            print(f"✗ Error generando Excel: {e}")

    def _generar_estadisticas(self, df: pd.DataFrame) -> Dict:
        """Genera estadísticas de los datos procesados."""
        stats = {
            'Total URLs procesadas': len(df),
            'URLs válidas': len(df[df['pagina_valida'] == True]),
            'URLs con error': len(df[df['error'].notna()]),
            'RFCs únicos': df['rfc'].nunique(),
            'Contribuyentes activos': len(df[df['situacion_contribuyente'] == 'ACTIVO']),
            'Estados únicos': df['entidad_federativa'].nunique(),
            'Regímenes fiscales únicos': df['regimen_fiscal'].nunique(),
        }
        return stats

    def _formatear_excel(self, writer, df):
        """Aplica formato básico al archivo Excel."""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment

            # Obtener la hoja de trabajo
            worksheet = writer.sheets['Datos Completos']

            # Formato para encabezados
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

            # Aplicar formato a la primera fila (encabezados)
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center")

            # Ajustar ancho de columnas
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter

                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        except Exception as e:
            print(f"Advertencia: No se pudo aplicar formato Excel: {e}")

    def generar_reporte_texto(self, datos: List[Dict], archivo_salida: str = "reporte_validacion_sat.txt"):
        """
        Genera un reporte de texto legible con la información extraída.

        Args:
            datos: Lista de diccionarios con información extraída
            archivo_salida: Nombre del archivo de texto de salida
        """
        try:
            with open(archivo_salida, 'w', encoding='utf-8') as f:
                f.write("REPORTE DE VALIDACIÓN DE CONSTANCIAS SAT\n")
                f.write("=" * 60 + "\n\n")
                f.write(f"Fecha de procesamiento: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Total de URLs procesadas: {len(datos)}\n\n")

                # Estadísticas
                datos_validos = [d for d in datos if d.get('pagina_valida')]
                f.write("ESTADÍSTICAS:\n")
                f.write("-" * 20 + "\n")
                f.write(f"URLs válidas: {len(datos_validos)}\n")
                f.write(f"URLs con error: {len(datos) - len(datos_validos)}\n")
                f.write(f"Contribuyentes activos: {sum(1 for d in datos_validos if d.get('situacion_contribuyente') == 'ACTIVO')}\n")
                f.write(f"Estados únicos: {len(set(d.get('entidad_federativa') for d in datos_validos if d.get('entidad_federativa')))}\n\n")

                # Detalle por contribuyente
                f.write("DETALLE POR CONTRIBUYENTE:\n")
                f.write("-" * 40 + "\n\n")

                for i, dato in enumerate(datos, 1):
                    f.write(f"{i}. RFC: {dato.get('rfc', 'N/A')}\n")

                    if dato.get('pagina_valida'):
                        f.write(f"   Nombre: {dato.get('nombre_completo', 'N/A')}\n")
                        f.write(f"   CURP: {dato.get('curp', 'N/A')}\n")
                        f.write(f"   Situación: {dato.get('situacion_contribuyente', 'N/A')}\n")
                        f.write(f"   Fecha nacimiento: {dato.get('fecha_nacimiento', 'N/A')}\n")
                        f.write(f"   Estado: {dato.get('entidad_federativa', 'N/A')}\n")
                        f.write(f"   Municipio: {dato.get('municipio', 'N/A')}\n")
                        f.write(f"   CP: {dato.get('codigo_postal', 'N/A')}\n")
                        f.write(f"   Régimen: {dato.get('regimen_fiscal', 'N/A')}\n")
                        f.write(f"   Email: {dato.get('correo_electronico', 'N/A')}\n")
                    else:
                        f.write(f"   ERROR: {dato.get('error', 'Página no válida')}\n")

                    f.write(f"   URL: {dato.get('url_consultada', 'N/A')}\n")
                    f.write("\n")

            print(f"✓ Reporte de texto generado: {archivo_salida}")

        except Exception as e:
            print(f"✗ Error generando reporte: {e}")


def main():
    """Función principal."""
    print("=" * 60)
    print("    VALIDADOR WEB DE CONSTANCIAS SAT")
    print("=" * 60)
    print()

    # Buscar archivo de QR extraídos
    archivos_qr = [
        'qr_extraidos_opencv.txt',
        'qr_extraidos_windows.txt',
        'qr_extraidos.txt'
    ]

    archivo_encontrado = None
    for archivo in archivos_qr:
        if os.path.exists(archivo):
            archivo_encontrado = archivo
            break

    if not archivo_encontrado:
        print("Error: No se encontró ningún archivo de QR extraídos.")
        print("Ejecuta primero el script de extracción de QR.")
        return

    print(f"Procesando URLs desde: {archivo_encontrado}")
    print("ADVERTENCIA: Este proceso puede tomar varios minutos debido a los delays entre requests.")
    print("Se respeta un delay de 2 segundos entre consultas para no sobrecargar el servidor del SAT.\n")

    # Crear validador
    validador = ValidadorSATWeb()

    # Procesar URLs
    datos_extraidos = validador.procesar_urls_desde_archivo(archivo_encontrado)

    if not datos_extraidos:
        print("No se pudieron procesar las URLs.")
        return

    print(f"\n" + "=" * 60)
    print("GENERANDO REPORTES")
    print("=" * 60)

    # Generar archivos de salida
    validador.generar_excel_detallado(datos_extraidos)
    validador.generar_reporte_texto(datos_extraidos)

    # Mostrar resumen final
    datos_validos = [d for d in datos_extraidos if d.get('pagina_valida')]

    print(f"\nRESUMEN FINAL:")
    print("-" * 30)
    print(f"URLs procesadas: {len(datos_extraidos)}")
    print(f"Información extraída exitosamente: {len(datos_validos)}")
    print(f"Errores: {len(datos_extraidos) - len(datos_validos)}")

    if datos_validos:
        activos = sum(1 for d in datos_validos if d.get('situacion_contribuyente') == 'ACTIVO')
        print(f"Contribuyentes activos: {activos}")

        print(f"\nArchivos generados:")
        print(f"- validacion_sat_detallada.xlsx (Excel completo)")
        print(f"- reporte_validacion_sat.txt (Reporte legible)")


if __name__ == "__main__":
    main()
