#!/usr/bin/env python3
"""
Sistema completo de validación de constancias SAT.
Integra extracción de QR y validación web en un solo proceso.
"""

import os
import sys
from pathlib import Path

def verificar_dependencias():
    """Verifica que todas las dependencias estén instaladas."""
    dependencias = {
        'pdf2image': 'pdf2image',
        'cv2': 'opencv-python',
        'PIL': 'Pillow',
        'playwright': 'playwright'
    }
    
    faltantes = []
    
    for modulo, paquete in dependencias.items():
        try:
            __import__(modulo)
        except ImportError:
            faltantes.append(paquete)
    
    if faltantes:
        print("Error: Faltan dependencias por instalar:")
        for dep in faltantes:
            print(f"  pip install {dep}")
        if 'playwright' in faltantes:
            print("  python -m playwright install")
        return False
    
    return True

def verificar_poppler():
    """Verifica que Poppler esté disponible."""
    poppler_path = Path("poppler/Library/bin")
    if poppler_path.exists():
        return True
    
    print("Advertencia: Poppler no encontrado en la ubicación local.")
    print("Ejecuta: python instalar_poppler.py")
    return False

def ejecutar_extraccion_qr():
    """Ejecuta la extracción de códigos QR."""
    print("=" * 60)
    print("PASO 1: EXTRACCIÓN DE CÓDIGOS QR")
    print("=" * 60)
    
    if not Path("Constancias").exists():
        print("Error: La carpeta 'Constancias' no existe.")
        print("Crea la carpeta y coloca los archivos PDF ahí.")
        return False
    
    archivos_pdf = list(Path("Constancias").glob("*.pdf"))
    if not archivos_pdf:
        print("Error: No se encontraron archivos PDF en la carpeta 'Constancias'.")
        return False
    
    print(f"Encontrados {len(archivos_pdf)} archivos PDF")
    
    try:
        # Importar y ejecutar extractor
        from extraer_qr_opencv import main as extraer_main
        extraer_main()
        
        # Verificar que se generó el archivo de QR
        if Path("qr_extraidos_opencv.txt").exists():
            print("✓ Extracción de QR completada exitosamente")
            return True
        else:
            print("✗ Error: No se generó el archivo de QR extraídos")
            return False
            
    except Exception as e:
        print(f"✗ Error en extracción de QR: {e}")
        return False

def ejecutar_validacion_web():
    """Ejecuta la validación web de las URLs."""
    print("\n" + "=" * 60)
    print("PASO 2: VALIDACIÓN WEB DE CONSTANCIAS")
    print("=" * 60)
    
    if not Path("qr_extraidos_opencv.txt").exists():
        print("Error: No se encontró el archivo de QR extraídos.")
        return False
    
    try:
        # Importar y ejecutar validador
        from validador_sat_playwright import main as validar_main
        validar_main()
        
        # Verificar que se generaron los archivos
        archivos_esperados = [
            "validacion_sat_playwright.csv",
            "resumen_sat_playwright.csv",
            "reporte_validacion_sat_playwright.txt"
        ]
        
        archivos_generados = [archivo for archivo in archivos_esperados if Path(archivo).exists()]
        
        if archivos_generados:
            print("✓ Validación web completada exitosamente")
            return True
        else:
            print("✗ Error: No se generaron los archivos de validación")
            return False
            
    except Exception as e:
        print(f"✗ Error en validación web: {e}")
        return False

def mostrar_resumen_final():
    """Muestra un resumen final de los archivos generados."""
    print("\n" + "=" * 60)
    print("PROCESO COMPLETADO")
    print("=" * 60)
    
    archivos_generados = {
        "Extracción de QR": [
            "qr_extraidos_opencv.txt"
        ],
        "Validación Web": [
            "validacion_sat_playwright.csv",
            "resumen_sat_playwright.csv", 
            "reporte_validacion_sat_playwright.txt"
        ]
    }
    
    print("ARCHIVOS GENERADOS:")
    print("-" * 30)
    
    for categoria, archivos in archivos_generados.items():
        print(f"\n{categoria}:")
        for archivo in archivos:
            if Path(archivo).exists():
                print(f"  ✓ {archivo}")
            else:
                print(f"  ✗ {archivo} (no generado)")
    
    print(f"\nARCHIVOS PRINCIPALES PARA EXCEL:")
    print(f"- resumen_sat_playwright.csv (Tabla resumen)")
    print(f"- validacion_sat_playwright.csv (Datos completos)")
    
    print(f"\nPARA VER INFORMACIÓN DETALLADA:")
    print(f"- reporte_validacion_sat_playwright.txt")

def main():
    """Función principal del sistema completo."""
    print("=" * 60)
    print("    SISTEMA COMPLETO DE VALIDACIÓN SAT")
    print("=" * 60)
    print()
    print("Este sistema:")
    print("1. Extrae códigos QR de PDFs en la carpeta 'Constancias'")
    print("2. Valida las URLs en el sitio web del SAT")
    print("3. Genera archivos Excel con información detallada")
    print()
    
    # Verificar dependencias
    print("Verificando dependencias...")
    if not verificar_dependencias():
        print("\nInstala las dependencias faltantes y vuelve a ejecutar.")
        return
    
    # Verificar Poppler
    if not verificar_poppler():
        print("Instala Poppler y vuelve a ejecutar.")
        return
    
    print("✓ Todas las dependencias están disponibles\n")
    
    # Paso 1: Extracción de QR
    if not ejecutar_extraccion_qr():
        print("\nError en la extracción de QR. Revisa los archivos PDF.")
        return
    
    # Paso 2: Validación web
    if not ejecutar_validacion_web():
        print("\nError en la validación web. Revisa la conexión a internet.")
        return
    
    # Mostrar resumen final
    mostrar_resumen_final()
    
    print(f"\n🎉 ¡Proceso completado exitosamente!")
    print(f"Abre 'resumen_sat_playwright.csv' en Excel para ver los resultados.")

if __name__ == "__main__":
    main()
