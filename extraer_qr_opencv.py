#!/usr/bin/env python3
"""
Script para extraer códigos QR de PDFs usando solo OpenCV.
Versión más confiable para Windows sin dependencias problemáticas.
"""

import os
import glob
from pathlib import Path

try:
    from pdf2image import convert_from_path
    import cv2
    import numpy as np
    from PIL import Image
except ImportError as e:
    print(f"Error: Instala las dependencias con:")
    print(f"pip install pdf2image opencv-python Pillow")
    print(f"Dependencia faltante: {e}")
    exit(1)


def mejorar_imagen_para_qr(imagen_cv):
    """
    Mejora la imagen para facilitar la detección de códigos QR.
    
    Args:
        imagen_cv: Imagen en formato OpenCV (BGR)
        
    Returns:
        Lista de imágenes procesadas para probar
    """
    imagenes_procesadas = []
    
    # Imagen original
    imagenes_procesadas.append(("Original", imagen_cv))
    
    # Convertir a escala de grises
    gray = cv2.cvtColor(imagen_cv, cv2.COLOR_BGR2GRAY)
    imagenes_procesadas.append(("Escala de grises", gray))
    
    # Mejorar contraste
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)
    imagenes_procesadas.append(("Contraste mejorado", enhanced))
    
    # Binarización adaptativa
    binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
    imagenes_procesadas.append(("Binarización adaptativa", binary))
    
    # Filtro de mediana para reducir ruido
    median = cv2.medianBlur(gray, 5)
    imagenes_procesadas.append(("Filtro mediana", median))
    
    return imagenes_procesadas


def extraer_qr_con_opencv(imagen_pil):
    """
    Extrae códigos QR usando OpenCV con múltiples técnicas de procesamiento.
    
    Args:
        imagen_pil: Imagen PIL
        
    Returns:
        Lista de strings con el contenido de los QR encontrados
    """
    try:
        # Convertir PIL a OpenCV
        imagen_cv = cv2.cvtColor(np.array(imagen_pil), cv2.COLOR_RGB2BGR)
        
        # Crear detector QR de OpenCV
        detector = cv2.QRCodeDetector()
        
        # Obtener diferentes versiones procesadas de la imagen
        imagenes_procesadas = mejorar_imagen_para_qr(imagen_cv)
        
        # Probar con cada versión procesada
        for nombre, img_procesada in imagenes_procesadas:
            try:
                # Detectar y decodificar
                data, vertices_array, binary_qrcode = detector.detectAndDecode(img_procesada)
                
                if data:
                    print(f"    ✓ QR detectado con: {nombre}")
                    return [data]
                    
            except Exception as e:
                print(f"    Error con {nombre}: {e}")
                continue
        
        # Si no funciona con una sola detección, intentar detectar múltiples QR
        try:
            # Detectar múltiples QR (OpenCV 4.5+)
            if hasattr(detector, 'detectAndDecodeMulti'):
                for nombre, img_procesada in imagenes_procesadas:
                    try:
                        success, decoded_info, points, _ = detector.detectAndDecodeMulti(img_procesada)
                        if success and decoded_info:
                            print(f"    ✓ Múltiples QR detectados con: {nombre}")
                            return [info for info in decoded_info if info]
                    except:
                        continue
        except:
            pass
        
        return []
        
    except Exception as e:
        print(f"    Error general con OpenCV: {e}")
        return []


def extraer_qr_de_pdf(ruta_pdf):
    """
    Extrae códigos QR de la primera página de un PDF.

    Args:
        ruta_pdf: Ruta al archivo PDF

    Returns:
        Lista de strings con el contenido de los QR encontrados
    """
    try:
        print(f"Procesando: {os.path.basename(ruta_pdf)}")

        # Buscar Poppler en ubicaciones comunes
        poppler_paths = [
            Path("poppler/Library/bin"),  # Instalación local
            Path("C:/Program Files/poppler/bin"),
            Path("C:/poppler/bin"),
            None  # Usar PATH del sistema
        ]

        # Convertir primera página a imagen con diferentes resoluciones
        resoluciones = [300, 200, 150]  # Probar diferentes DPI

        for poppler_path in poppler_paths:
            for dpi in resoluciones:
                try:
                    print(f"    Intentando con DPI: {dpi}, Poppler: {poppler_path or 'PATH del sistema'}")

                    if poppler_path and poppler_path.exists():
                        imagenes = convert_from_path(
                            ruta_pdf,
                            first_page=1,
                            last_page=1,
                            dpi=dpi,
                            poppler_path=str(poppler_path)
                        )
                    else:
                        imagenes = convert_from_path(ruta_pdf, first_page=1, last_page=1, dpi=dpi)

                    if not imagenes:
                        continue

                    imagen = imagenes[0]

                    # Intentar extraer QR
                    qr_contenidos = extraer_qr_con_opencv(imagen)

                    if qr_contenidos:
                        for i, contenido in enumerate(qr_contenidos, 1):
                            print(f"  ✓ QR {i} encontrado: {contenido[:80]}...")
                        return qr_contenidos

                except Exception as e:
                    print(f"    Error con DPI {dpi}, Poppler {poppler_path}: {e}")
                    continue

            # Si encontramos una configuración de Poppler que funciona, no probar más
            try:
                if poppler_path and poppler_path.exists():
                    test_imagenes = convert_from_path(
                        ruta_pdf,
                        first_page=1,
                        last_page=1,
                        dpi=150,
                        poppler_path=str(poppler_path)
                    )
                else:
                    test_imagenes = convert_from_path(ruta_pdf, first_page=1, last_page=1, dpi=150)

                if test_imagenes:
                    break  # Esta configuración de Poppler funciona
            except:
                continue

        print(f"  ⚠ No se encontraron códigos QR")
        return []

    except Exception as e:
        print(f"  ✗ Error procesando {os.path.basename(ruta_pdf)}: {e}")
        return []


def main():
    """Función principal."""
    carpeta_constancias = "Constancias"
    
    # Verificar que existe la carpeta
    if not os.path.exists(carpeta_constancias):
        print(f"Error: La carpeta '{carpeta_constancias}' no existe")
        return
    
    # Buscar todos los archivos PDF
    patron_pdf = os.path.join(carpeta_constancias, "*.pdf")
    archivos_pdf = glob.glob(patron_pdf)
    
    if not archivos_pdf:
        print(f"No se encontraron archivos PDF en '{carpeta_constancias}'")
        return
    
    print(f"Encontrados {len(archivos_pdf)} archivos PDF")
    print("Usando OpenCV para detección de QR con múltiples técnicas de procesamiento\n")
    
    # Procesar cada PDF
    todos_los_qr = {}
    
    for archivo_pdf in archivos_pdf:
        nombre_archivo = os.path.basename(archivo_pdf)
        qr_contenido = extraer_qr_de_pdf(archivo_pdf)
        todos_los_qr[nombre_archivo] = qr_contenido
        print()  # Línea en blanco entre archivos
    
    # Mostrar resumen
    print("=" * 60)
    print("RESUMEN DE CÓDIGOS QR EXTRAÍDOS")
    print("=" * 60)
    
    total_archivos = len(archivos_pdf)
    archivos_con_qr = sum(1 for qr_list in todos_los_qr.values() if qr_list)
    total_qr = sum(len(qr_list) for qr_list in todos_los_qr.values())
    
    print(f"Total de archivos procesados: {total_archivos}")
    print(f"Archivos con códigos QR: {archivos_con_qr}")
    print(f"Total de códigos QR encontrados: {total_qr}")
    print()
    
    # Mostrar contenido de cada QR
    if total_qr > 0:
        print("CÓDIGOS QR ENCONTRADOS:")
        print("-" * 40)
        for archivo, qr_list in todos_los_qr.items():
            if qr_list:
                print(f"\n📄 {archivo}:")
                for i, qr_contenido in enumerate(qr_list, 1):
                    print(f"   QR {i}: {qr_contenido}")
    
    # Guardar resultados en archivo de texto
    with open("qr_extraidos_opencv.txt", "w", encoding="utf-8") as f:
        f.write("CÓDIGOS QR EXTRAÍDOS DE CONSTANCIAS (OpenCV)\n")
        f.write("=" * 50 + "\n\n")
        
        for archivo, qr_list in todos_los_qr.items():
            f.write(f"Archivo: {archivo}\n")
            if qr_list:
                for i, qr_contenido in enumerate(qr_list, 1):
                    f.write(f"  QR {i}: {qr_contenido}\n")
            else:
                f.write("  No se encontraron códigos QR\n")
            f.write("\n")
    
    print(f"\nResultados guardados en 'qr_extraidos_opencv.txt'")
    
    # Mostrar estadísticas adicionales
    if archivos_con_qr > 0:
        porcentaje_exito = (archivos_con_qr / total_archivos) * 100
        print(f"Tasa de éxito: {porcentaje_exito:.1f}%")


if __name__ == "__main__":
    main()
