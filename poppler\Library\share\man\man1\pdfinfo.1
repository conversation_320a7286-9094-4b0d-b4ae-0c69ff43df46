.\" Copyright 1999-2011 Glyph & Cog, LLC
.TH pdfinfo 1 "15 August 2011"
.SH NAME
pdfinfo \- Portable Document Format (PDF) document information
extractor (version 3.03)
.SH SYNOPSIS
.B pdfinfo
[options]
.RI [ PDF-file ]
.SH DESCRIPTION
.B Pdfinfo
prints the contents of the \'Info' dictionary (plus some other useful
information) from a Portable Document Format (PDF) file.
.PP
If
.I PDF-file
is \'-', it reads the PDF file from stdin.
.PP
The \'Info' dictionary contains the following values:
.PP
.RS
title
.RE
.RS
subject
.RE
.RS
keywords
.RE
.RS
author
.RE
.RS
creator
.RE
.RS
producer
.RE
.RS
creation date
.RE
.RS
modification date
.RE
.PP
In addition, the following information is printed:
.PP
.RS
custom metadata (yes/no)
.RE
.RS
metadata stream (yes/no)
.RE
.RS
tagged (yes/no)
.RE
.RS
userproperties (yes/no)
.RE
.RS
suspects (yes/no)
.RE
.RS
form (AcroForm / XFA / none)
.RE
.RS
javascript (yes/no)
.RE
.RS
page count
.RE
.RS
encrypted flag (yes/no)
.RE
.RS
print and copy permissions (if encrypted)
.RE
.RS
page size
.RE
.RS
file size
.RE
.RS
linearized (yes/no)
.RE
.RS
PDF version
.RE
.RS
metadata (only if requested)
.RE
.PP
The options \-listenc, \-meta, \-js, \-struct, and \-struct-text only print the requested information. The 'Info' dictionary and related data listed above is not printed. At most one of these five options may be used.
.SH OPTIONS
.TP
.BI \-f " number"
Specifies the first page to examine.  If multiple pages are requested
using the "\-f" and "\-l" options, the size of each requested page (and,
optionally, the bounding boxes for each requested page) are printed.
Otherwise, only page one is examined.
.TP
.BI \-l " number"
Specifies the last page to examine.
.TP
.B \-box
Prints the page box bounding boxes: MediaBox, CropBox, BleedBox,
TrimBox, and ArtBox.
.TP
.B \-meta
Prints document-level metadata.  (This is the "Metadata" stream from
the PDF file's Catalog object.)
.TP
.B \-custom
Prints custom and standard metadata.
.TP
.B \-js
Prints all JavaScript in the PDF.
.TP
.B \-struct
Prints the logical document structure of a Tagged-PDF file.
.TP
.B \-struct-text
Print the textual content along with the document structure of a Tagged-PDF
file.  Note that extracting text this way might be slow for big PDF files.
(Implies
.BR \-struct .)
.TP
.B \-url
Print all URLs in the PDF. Only the URL types supported by Poppler are listed.
Currently, this is limited to Annotations. Note: only URLs referenced by the PDF objects
such as Link Annotations are listed. pdfinfo does not attempt to extract strings
matching http://... from the text content.
.TP
.B \-isodates
Prints dates in ISO-8601 format (including the time zone).
.TP
.B \-rawdates
Prints the raw (undecoded) date strings, directly from the PDF file.
.TP
.B \-dests
Print a list of all named destinations. If a page range is specified using "\-f" and "\-l", only
destinations in the page range are listed.
.TP
.BI \-enc " encoding-name"
Sets the encoding to use for text output. This defaults to "UTF-8".
.TP
.B \-listenc
Lits the available encodings
.TP
.BI \-opw " password"
Specify the owner password for the PDF file.  Providing this will
bypass all security restrictions.
.TP
.BI \-upw " password"
Specify the user password for the PDF file.
.TP
.B \-v
Print copyright and version information.
.TP
.B \-h
Print usage information.
.RB ( \-help
and
.B \-\-help
are equivalent.)
.SH EXIT CODES
The Xpdf tools use the following exit codes:
.TP
0
No error.
.TP
1
Error opening a PDF file.
.TP
2
Error opening an output file.
.TP
3
Error related to PDF permissions.
.TP
99
Other error.
.SH AUTHOR
The pdfinfo software and documentation are copyright 1996-2011 Glyph &
Cog, LLC.
.SH "SEE ALSO"
.BR pdfdetach (1),
.BR pdffonts (1),
.BR pdfimages (1),
.BR pdftocairo (1),
.BR pdftohtml (1),
.BR pdftoppm (1),
.BR pdftops (1),
.BR pdftotext (1)
.BR pdfseparate (1),
.BR pdfsig (1),
.BR pdfunite (1)
