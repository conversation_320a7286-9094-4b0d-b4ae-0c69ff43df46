//========================================================================
//
// NameToUnicodeTable.h
//
// Copyright 2001-2003 Glyph & Cog, LLC
//
//========================================================================

//========================================================================
//
// Modified under the Poppler project - http://poppler.freedesktop.org
//
// All changes made under the Poppler project to this file are licensed
// under GPL version 2 or later
//
// Copyright (C) 2011, 2012, 2020 <PERSON> C<PERSON> <<EMAIL>>
// Copyright (C) 2013 <PERSON> <<EMAIL>>
// Copyright (C) 2022, 2023 <PERSON> <<EMAIL>>
//
// To see a description of the changes please see the Changelog file that
// came with your tarball or type make ChangeLog if you are building from git
//
//========================================================================

#include "CharTypes.h"

#include <cstddef>

struct NameToUnicodeTab
{
    Unicode u;
    const char *name;
};

// map character names to Unicode
static const struct NameToUnicodeTab nameToUnicodeTextTab[] = { { 0x0021, "!" },
                                                                { 0x0023, "#" },
                                                                { 0x0024, "$" },
                                                                { 0x0025, "%" },
                                                                { 0x0026, "&" },
                                                                { 0x0027, "'" },
                                                                { 0x0028, "(" },
                                                                { 0x0029, ")" },
                                                                { 0x002a, "*" },
                                                                { 0x002b, "+" },
                                                                { 0x002c, "," },
                                                                { 0x002d, "-" },
                                                                { 0x002e, "." },
                                                                { 0x002f, "/" },
                                                                { 0x0030, "0" },
                                                                { 0x0031, "1" },
                                                                { 0x0032, "2" },
                                                                { 0x0033, "3" },
                                                                { 0x0034, "4" },
                                                                { 0x0035, "5" },
                                                                { 0x0036, "6" },
                                                                { 0x0037, "7" },
                                                                { 0x0038, "8" },
                                                                { 0x0039, "9" },
                                                                { 0x003a, ":" },
                                                                { 0x003b, ";" },
                                                                { 0x003c, "<" },
                                                                { 0x003d, "=" },
                                                                { 0x003e, ">" },
                                                                { 0x003f, "?" },
                                                                { 0x0040, "@" },
                                                                { 0x0041, "A" },
                                                                { 0x00c6, "AE" },
                                                                { 0x01fc, "AEacute" },
                                                                { 0x01e2, "AEmacron" },
                                                                { 0xf7e6, "AEsmall" },
                                                                { 0x00c1, "Aacute" },
                                                                { 0xf7e1, "Aacutesmall" },
                                                                { 0x0102, "Abreve" },
                                                                { 0x1eae, "Abreveacute" },
                                                                { 0x04d0, "Abrevecyrillic" },
                                                                { 0x1eb6, "Abrevedotbelow" },
                                                                { 0x1eb0, "Abrevegrave" },
                                                                { 0x1eb2, "Abrevehookabove" },
                                                                { 0x1eb4, "Abrevetilde" },
                                                                { 0x01cd, "Acaron" },
                                                                { 0x24b6, "Acircle" },
                                                                { 0x00c2, "Acircumflex" },
                                                                { 0x1ea4, "Acircumflexacute" },
                                                                { 0x1eac, "Acircumflexdotbelow" },
                                                                { 0x1ea6, "Acircumflexgrave" },
                                                                { 0x1ea8, "Acircumflexhookabove" },
                                                                { 0xf7e2, "Acircumflexsmall" },
                                                                { 0x1eaa, "Acircumflextilde" },
                                                                { 0xf6c9, "Acute" },
                                                                { 0xf7b4, "Acutesmall" },
                                                                { 0x0410, "Acyrillic" },
                                                                { 0x0200, "Adblgrave" },
                                                                { 0x00c4, "Adieresis" },
                                                                { 0x04d2, "Adieresiscyrillic" },
                                                                { 0x01de, "Adieresismacron" },
                                                                { 0xf7e4, "Adieresissmall" },
                                                                { 0x1ea0, "Adotbelow" },
                                                                { 0x01e0, "Adotmacron" },
                                                                { 0x00c0, "Agrave" },
                                                                { 0xf7e0, "Agravesmall" },
                                                                { 0x1ea2, "Ahookabove" },
                                                                { 0x04d4, "Aiecyrillic" },
                                                                { 0x0202, "Ainvertedbreve" },
                                                                { 0x0391, "Alpha" },
                                                                { 0x0386, "Alphatonos" },
                                                                { 0x0100, "Amacron" },
                                                                { 0xff21, "Amonospace" },
                                                                { 0x0104, "Aogonek" },
                                                                { 0x00c5, "Aring" },
                                                                { 0x01fa, "Aringacute" },
                                                                { 0x1e00, "Aringbelow" },
                                                                { 0xf7e5, "Aringsmall" },
                                                                { 0xf761, "Asmall" },
                                                                { 0x00c3, "Atilde" },
                                                                { 0xf7e3, "Atildesmall" },
                                                                { 0x0531, "Aybarmenian" },
                                                                { 0x0042, "B" },
                                                                { 0x24b7, "Bcircle" },
                                                                { 0x1e02, "Bdotaccent" },
                                                                { 0x1e04, "Bdotbelow" },
                                                                { 0x0411, "Becyrillic" },
                                                                { 0x0532, "Benarmenian" },
                                                                { 0x0392, "Beta" },
                                                                { 0x0181, "Bhook" },
                                                                { 0x1e06, "Blinebelow" },
                                                                { 0xff22, "Bmonospace" },
                                                                { 0xf6f4, "Brevesmall" },
                                                                { 0xf762, "Bsmall" },
                                                                { 0x0182, "Btopbar" },
                                                                { 0x0043, "C" },
                                                                { 0x053e, "Caarmenian" },
                                                                { 0x0106, "Cacute" },
                                                                { 0xf6ca, "Caron" },
                                                                { 0xf6f5, "Caronsmall" },
                                                                { 0x010c, "Ccaron" },
                                                                { 0x00c7, "Ccedilla" },
                                                                { 0x1e08, "Ccedillaacute" },
                                                                { 0xf7e7, "Ccedillasmall" },
                                                                { 0x24b8, "Ccircle" },
                                                                { 0x0108, "Ccircumflex" },
                                                                { 0x010a, "Cdot" },
                                                                { 0x010a, "Cdotaccent" },
                                                                { 0xf7b8, "Cedillasmall" },
                                                                { 0x0549, "Chaarmenian" },
                                                                { 0x04bc, "Cheabkhasiancyrillic" },
                                                                { 0x0427, "Checyrillic" },
                                                                { 0x04be, "Chedescenderabkhasiancyrillic" },
                                                                { 0x04b6, "Chedescendercyrillic" },
                                                                { 0x04f4, "Chedieresiscyrillic" },
                                                                { 0x0543, "Cheharmenian" },
                                                                { 0x04cb, "Chekhakassiancyrillic" },
                                                                { 0x04b8, "Cheverticalstrokecyrillic" },
                                                                { 0x03a7, "Chi" },
                                                                { 0x0187, "Chook" },
                                                                { 0xf6f6, "Circumflexsmall" },
                                                                { 0xff23, "Cmonospace" },
                                                                { 0x0551, "Coarmenian" },
                                                                { 0xf763, "Csmall" },
                                                                { 0x0044, "D" },
                                                                { 0x01f1, "DZ" },
                                                                { 0x01c4, "DZcaron" },
                                                                { 0x0534, "Daarmenian" },
                                                                { 0x0189, "Dafrican" },
                                                                { 0x010e, "Dcaron" },
                                                                { 0x1e10, "Dcedilla" },
                                                                { 0x24b9, "Dcircle" },
                                                                { 0x1e12, "Dcircumflexbelow" },
                                                                { 0x0110, "Dcroat" },
                                                                { 0x1e0a, "Ddotaccent" },
                                                                { 0x1e0c, "Ddotbelow" },
                                                                { 0x0414, "Decyrillic" },
                                                                { 0x03ee, "Deicoptic" },
                                                                { 0x2206, "Delta" },
                                                                { 0x0394, "Deltagreek" },
                                                                { 0x018a, "Dhook" },
                                                                { 0xf6cb, "Dieresis" },
                                                                { 0xf6cc, "DieresisAcute" },
                                                                { 0xf6cd, "DieresisGrave" },
                                                                { 0xf7a8, "Dieresissmall" },
                                                                { 0x03dc, "Digammagreek" },
                                                                { 0x0402, "Djecyrillic" },
                                                                { 0x1e0e, "Dlinebelow" },
                                                                { 0xff24, "Dmonospace" },
                                                                { 0xf6f7, "Dotaccentsmall" },
                                                                { 0x0110, "Dslash" },
                                                                { 0xf764, "Dsmall" },
                                                                { 0x018b, "Dtopbar" },
                                                                { 0x01f2, "Dz" },
                                                                { 0x01c5, "Dzcaron" },
                                                                { 0x04e0, "Dzeabkhasiancyrillic" },
                                                                { 0x0405, "Dzecyrillic" },
                                                                { 0x040f, "Dzhecyrillic" },
                                                                { 0x0045, "E" },
                                                                { 0x00c9, "Eacute" },
                                                                { 0xf7e9, "Eacutesmall" },
                                                                { 0x0114, "Ebreve" },
                                                                { 0x011a, "Ecaron" },
                                                                { 0x1e1c, "Ecedillabreve" },
                                                                { 0x0535, "Echarmenian" },
                                                                { 0x24ba, "Ecircle" },
                                                                { 0x00ca, "Ecircumflex" },
                                                                { 0x1ebe, "Ecircumflexacute" },
                                                                { 0x1e18, "Ecircumflexbelow" },
                                                                { 0x1ec6, "Ecircumflexdotbelow" },
                                                                { 0x1ec0, "Ecircumflexgrave" },
                                                                { 0x1ec2, "Ecircumflexhookabove" },
                                                                { 0xf7ea, "Ecircumflexsmall" },
                                                                { 0x1ec4, "Ecircumflextilde" },
                                                                { 0x0404, "Ecyrillic" },
                                                                { 0x0204, "Edblgrave" },
                                                                { 0x00cb, "Edieresis" },
                                                                { 0xf7eb, "Edieresissmall" },
                                                                { 0x0116, "Edot" },
                                                                { 0x0116, "Edotaccent" },
                                                                { 0x1eb8, "Edotbelow" },
                                                                { 0x0424, "Efcyrillic" },
                                                                { 0x00c8, "Egrave" },
                                                                { 0xf7e8, "Egravesmall" },
                                                                { 0x0537, "Eharmenian" },
                                                                { 0x1eba, "Ehookabove" },
                                                                { 0x2167, "Eightroman" },
                                                                { 0x0206, "Einvertedbreve" },
                                                                { 0x0464, "Eiotifiedcyrillic" },
                                                                { 0x041b, "Elcyrillic" },
                                                                { 0x216a, "Elevenroman" },
                                                                { 0x0112, "Emacron" },
                                                                { 0x1e16, "Emacronacute" },
                                                                { 0x1e14, "Emacrongrave" },
                                                                { 0x041c, "Emcyrillic" },
                                                                { 0xff25, "Emonospace" },
                                                                { 0x041d, "Encyrillic" },
                                                                { 0x04a2, "Endescendercyrillic" },
                                                                { 0x014a, "Eng" },
                                                                { 0x04a4, "Enghecyrillic" },
                                                                { 0x04c7, "Enhookcyrillic" },
                                                                { 0x0118, "Eogonek" },
                                                                { 0x0190, "Eopen" },
                                                                { 0x0395, "Epsilon" },
                                                                { 0x0388, "Epsilontonos" },
                                                                { 0x0420, "Ercyrillic" },
                                                                { 0x018e, "Ereversed" },
                                                                { 0x042d, "Ereversedcyrillic" },
                                                                { 0x0421, "Escyrillic" },
                                                                { 0x04aa, "Esdescendercyrillic" },
                                                                { 0x01a9, "Esh" },
                                                                { 0xf765, "Esmall" },
                                                                { 0x0397, "Eta" },
                                                                { 0x0538, "Etarmenian" },
                                                                { 0x0389, "Etatonos" },
                                                                { 0x00d0, "Eth" },
                                                                { 0xf7f0, "Ethsmall" },
                                                                { 0x1ebc, "Etilde" },
                                                                { 0x1e1a, "Etildebelow" },
                                                                { 0x20ac, "Euro" },
                                                                { 0x01b7, "Ezh" },
                                                                { 0x01ee, "Ezhcaron" },
                                                                { 0x01b8, "Ezhreversed" },
                                                                { 0x0046, "F" },
                                                                { 0x24bb, "Fcircle" },
                                                                { 0x1e1e, "Fdotaccent" },
                                                                { 0x0556, "Feharmenian" },
                                                                { 0x03e4, "Feicoptic" },
                                                                { 0x0191, "Fhook" },
                                                                { 0x0472, "Fitacyrillic" },
                                                                { 0x2164, "Fiveroman" },
                                                                { 0xff26, "Fmonospace" },
                                                                { 0x2163, "Fourroman" },
                                                                { 0xf766, "Fsmall" },
                                                                { 0x0047, "G" },
                                                                { 0x3387, "GBsquare" },
                                                                { 0x01f4, "Gacute" },
                                                                { 0x0393, "Gamma" },
                                                                { 0x0194, "Gammaafrican" },
                                                                { 0x03ea, "Gangiacoptic" },
                                                                { 0x011e, "Gbreve" },
                                                                { 0x01e6, "Gcaron" },
                                                                { 0x0122, "Gcedilla" },
                                                                { 0x24bc, "Gcircle" },
                                                                { 0x011c, "Gcircumflex" },
                                                                { 0x0122, "Gcommaaccent" },
                                                                { 0x0120, "Gdot" },
                                                                { 0x0120, "Gdotaccent" },
                                                                { 0x0413, "Gecyrillic" },
                                                                { 0x0542, "Ghadarmenian" },
                                                                { 0x0494, "Ghemiddlehookcyrillic" },
                                                                { 0x0492, "Ghestrokecyrillic" },
                                                                { 0x0490, "Gheupturncyrillic" },
                                                                { 0x0193, "Ghook" },
                                                                { 0x0533, "Gimarmenian" },
                                                                { 0x0403, "Gjecyrillic" },
                                                                { 0x1e20, "Gmacron" },
                                                                { 0xff27, "Gmonospace" },
                                                                { 0xf6ce, "Grave" },
                                                                { 0xf760, "Gravesmall" },
                                                                { 0xf767, "Gsmall" },
                                                                { 0x029b, "Gsmallhook" },
                                                                { 0x01e4, "Gstroke" },
                                                                { 0x0048, "H" },
                                                                { 0x25cf, "H18533" },
                                                                { 0x25aa, "H18543" },
                                                                { 0x25ab, "H18551" },
                                                                { 0x25a1, "H22073" },
                                                                { 0x33cb, "HPsquare" },
                                                                { 0x04a8, "Haabkhasiancyrillic" },
                                                                { 0x04b2, "Hadescendercyrillic" },
                                                                { 0x042a, "Hardsigncyrillic" },
                                                                { 0x0126, "Hbar" },
                                                                { 0x1e2a, "Hbrevebelow" },
                                                                { 0x1e28, "Hcedilla" },
                                                                { 0x24bd, "Hcircle" },
                                                                { 0x0124, "Hcircumflex" },
                                                                { 0x1e26, "Hdieresis" },
                                                                { 0x1e22, "Hdotaccent" },
                                                                { 0x1e24, "Hdotbelow" },
                                                                { 0xff28, "Hmonospace" },
                                                                { 0x0540, "Hoarmenian" },
                                                                { 0x03e8, "Horicoptic" },
                                                                { 0xf768, "Hsmall" },
                                                                { 0xf6cf, "Hungarumlaut" },
                                                                { 0xf6f8, "Hungarumlautsmall" },
                                                                { 0x3390, "Hzsquare" },
                                                                { 0x0049, "I" },
                                                                { 0x042f, "IAcyrillic" },
                                                                { 0x0132, "IJ" },
                                                                { 0x042e, "IUcyrillic" },
                                                                { 0x00cd, "Iacute" },
                                                                { 0xf7ed, "Iacutesmall" },
                                                                { 0x012c, "Ibreve" },
                                                                { 0x01cf, "Icaron" },
                                                                { 0x24be, "Icircle" },
                                                                { 0x00ce, "Icircumflex" },
                                                                { 0xf7ee, "Icircumflexsmall" },
                                                                { 0x0406, "Icyrillic" },
                                                                { 0x0208, "Idblgrave" },
                                                                { 0x00cf, "Idieresis" },
                                                                { 0x1e2e, "Idieresisacute" },
                                                                { 0x04e4, "Idieresiscyrillic" },
                                                                { 0xf7ef, "Idieresissmall" },
                                                                { 0x0130, "Idot" },
                                                                { 0x0130, "Idotaccent" },
                                                                { 0x1eca, "Idotbelow" },
                                                                { 0x04d6, "Iebrevecyrillic" },
                                                                { 0x0415, "Iecyrillic" },
                                                                { 0x2111, "Ifraktur" },
                                                                { 0x00cc, "Igrave" },
                                                                { 0xf7ec, "Igravesmall" },
                                                                { 0x1ec8, "Ihookabove" },
                                                                { 0x0418, "Iicyrillic" },
                                                                { 0x020a, "Iinvertedbreve" },
                                                                { 0x0419, "Iishortcyrillic" },
                                                                { 0x012a, "Imacron" },
                                                                { 0x04e2, "Imacroncyrillic" },
                                                                { 0xff29, "Imonospace" },
                                                                { 0x053b, "Iniarmenian" },
                                                                { 0x0401, "Iocyrillic" },
                                                                { 0x012e, "Iogonek" },
                                                                { 0x0399, "Iota" },
                                                                { 0x0196, "Iotaafrican" },
                                                                { 0x03aa, "Iotadieresis" },
                                                                { 0x038a, "Iotatonos" },
                                                                { 0xf769, "Ismall" },
                                                                { 0x0197, "Istroke" },
                                                                { 0x0128, "Itilde" },
                                                                { 0x1e2c, "Itildebelow" },
                                                                { 0x0474, "Izhitsacyrillic" },
                                                                { 0x0476, "Izhitsadblgravecyrillic" },
                                                                { 0x004a, "J" },
                                                                { 0x0541, "Jaarmenian" },
                                                                { 0x24bf, "Jcircle" },
                                                                { 0x0134, "Jcircumflex" },
                                                                { 0x0408, "Jecyrillic" },
                                                                { 0x054b, "Jheharmenian" },
                                                                { 0xff2a, "Jmonospace" },
                                                                { 0xf76a, "Jsmall" },
                                                                { 0x004b, "K" },
                                                                { 0x3385, "KBsquare" },
                                                                { 0x33cd, "KKsquare" },
                                                                { 0x04a0, "Kabashkircyrillic" },
                                                                { 0x1e30, "Kacute" },
                                                                { 0x041a, "Kacyrillic" },
                                                                { 0x049a, "Kadescendercyrillic" },
                                                                { 0x04c3, "Kahookcyrillic" },
                                                                { 0x039a, "Kappa" },
                                                                { 0x049e, "Kastrokecyrillic" },
                                                                { 0x049c, "Kaverticalstrokecyrillic" },
                                                                { 0x01e8, "Kcaron" },
                                                                { 0x0136, "Kcedilla" },
                                                                { 0x24c0, "Kcircle" },
                                                                { 0x0136, "Kcommaaccent" },
                                                                { 0x1e32, "Kdotbelow" },
                                                                { 0x0554, "Keharmenian" },
                                                                { 0x053f, "Kenarmenian" },
                                                                { 0x0425, "Khacyrillic" },
                                                                { 0x03e6, "Kheicoptic" },
                                                                { 0x0198, "Khook" },
                                                                { 0x040c, "Kjecyrillic" },
                                                                { 0x1e34, "Klinebelow" },
                                                                { 0xff2b, "Kmonospace" },
                                                                { 0x0480, "Koppacyrillic" },
                                                                { 0x03de, "Koppagreek" },
                                                                { 0x046e, "Ksicyrillic" },
                                                                { 0xf76b, "Ksmall" },
                                                                { 0x004c, "L" },
                                                                { 0x01c7, "LJ" },
                                                                { 0xf6bf, "LL" },
                                                                { 0x0139, "Lacute" },
                                                                { 0x039b, "Lambda" },
                                                                { 0x013d, "Lcaron" },
                                                                { 0x013b, "Lcedilla" },
                                                                { 0x24c1, "Lcircle" },
                                                                { 0x1e3c, "Lcircumflexbelow" },
                                                                { 0x013b, "Lcommaaccent" },
                                                                { 0x013f, "Ldot" },
                                                                { 0x013f, "Ldotaccent" },
                                                                { 0x1e36, "Ldotbelow" },
                                                                { 0x1e38, "Ldotbelowmacron" },
                                                                { 0x053c, "Liwnarmenian" },
                                                                { 0x01c8, "Lj" },
                                                                { 0x0409, "Ljecyrillic" },
                                                                { 0x1e3a, "Llinebelow" },
                                                                { 0xff2c, "Lmonospace" },
                                                                { 0x0141, "Lslash" },
                                                                { 0xf6f9, "Lslashsmall" },
                                                                { 0xf76c, "Lsmall" },
                                                                { 0x004d, "M" },
                                                                { 0x3386, "MBsquare" },
                                                                { 0xf6d0, "Macron" },
                                                                { 0xf7af, "Macronsmall" },
                                                                { 0x1e3e, "Macute" },
                                                                { 0x24c2, "Mcircle" },
                                                                { 0x1e40, "Mdotaccent" },
                                                                { 0x1e42, "Mdotbelow" },
                                                                { 0x0544, "Menarmenian" },
                                                                { 0xff2d, "Mmonospace" },
                                                                { 0xf76d, "Msmall" },
                                                                { 0x019c, "Mturned" },
                                                                { 0x039c, "Mu" },
                                                                { 0x004e, "N" },
                                                                { 0x01ca, "NJ" },
                                                                { 0x0143, "Nacute" },
                                                                { 0x0147, "Ncaron" },
                                                                { 0x0145, "Ncedilla" },
                                                                { 0x24c3, "Ncircle" },
                                                                { 0x1e4a, "Ncircumflexbelow" },
                                                                { 0x0145, "Ncommaaccent" },
                                                                { 0x1e44, "Ndotaccent" },
                                                                { 0x1e46, "Ndotbelow" },
                                                                { 0x019d, "Nhookleft" },
                                                                { 0x2168, "Nineroman" },
                                                                { 0x01cb, "Nj" },
                                                                { 0x040a, "Njecyrillic" },
                                                                { 0x1e48, "Nlinebelow" },
                                                                { 0xff2e, "Nmonospace" },
                                                                { 0x0546, "Nowarmenian" },
                                                                { 0xf76e, "Nsmall" },
                                                                { 0x00d1, "Ntilde" },
                                                                { 0xf7f1, "Ntildesmall" },
                                                                { 0x039d, "Nu" },
                                                                { 0x004f, "O" },
                                                                { 0x0152, "OE" },
                                                                { 0xf6fa, "OEsmall" },
                                                                { 0x00d3, "Oacute" },
                                                                { 0xf7f3, "Oacutesmall" },
                                                                { 0x04e8, "Obarredcyrillic" },
                                                                { 0x04ea, "Obarreddieresiscyrillic" },
                                                                { 0x014e, "Obreve" },
                                                                { 0x01d1, "Ocaron" },
                                                                { 0x019f, "Ocenteredtilde" },
                                                                { 0x24c4, "Ocircle" },
                                                                { 0x00d4, "Ocircumflex" },
                                                                { 0x1ed0, "Ocircumflexacute" },
                                                                { 0x1ed8, "Ocircumflexdotbelow" },
                                                                { 0x1ed2, "Ocircumflexgrave" },
                                                                { 0x1ed4, "Ocircumflexhookabove" },
                                                                { 0xf7f4, "Ocircumflexsmall" },
                                                                { 0x1ed6, "Ocircumflextilde" },
                                                                { 0x041e, "Ocyrillic" },
                                                                { 0x0150, "Odblacute" },
                                                                { 0x020c, "Odblgrave" },
                                                                { 0x00d6, "Odieresis" },
                                                                { 0x04e6, "Odieresiscyrillic" },
                                                                { 0xf7f6, "Odieresissmall" },
                                                                { 0x1ecc, "Odotbelow" },
                                                                { 0xf6fb, "Ogoneksmall" },
                                                                { 0x00d2, "Ograve" },
                                                                { 0xf7f2, "Ogravesmall" },
                                                                { 0x0555, "Oharmenian" },
                                                                { 0x2126, "Ohm" },
                                                                { 0x1ece, "Ohookabove" },
                                                                { 0x01a0, "Ohorn" },
                                                                { 0x1eda, "Ohornacute" },
                                                                { 0x1ee2, "Ohorndotbelow" },
                                                                { 0x1edc, "Ohorngrave" },
                                                                { 0x1ede, "Ohornhookabove" },
                                                                { 0x1ee0, "Ohorntilde" },
                                                                { 0x0150, "Ohungarumlaut" },
                                                                { 0x01a2, "Oi" },
                                                                { 0x020e, "Oinvertedbreve" },
                                                                { 0x014c, "Omacron" },
                                                                { 0x1e52, "Omacronacute" },
                                                                { 0x1e50, "Omacrongrave" },
                                                                { 0x2126, "Omega" },
                                                                { 0x0460, "Omegacyrillic" },
                                                                { 0x03a9, "Omegagreek" },
                                                                { 0x047a, "Omegaroundcyrillic" },
                                                                { 0x047c, "Omegatitlocyrillic" },
                                                                { 0x038f, "Omegatonos" },
                                                                { 0x039f, "Omicron" },
                                                                { 0x038c, "Omicrontonos" },
                                                                { 0xff2f, "Omonospace" },
                                                                { 0x2160, "Oneroman" },
                                                                { 0x01ea, "Oogonek" },
                                                                { 0x01ec, "Oogonekmacron" },
                                                                { 0x0186, "Oopen" },
                                                                { 0x00d8, "Oslash" },
                                                                { 0x01fe, "Oslashacute" },
                                                                { 0xf7f8, "Oslashsmall" },
                                                                { 0xf76f, "Osmall" },
                                                                { 0x01fe, "Ostrokeacute" },
                                                                { 0x047e, "Otcyrillic" },
                                                                { 0x00d5, "Otilde" },
                                                                { 0x1e4c, "Otildeacute" },
                                                                { 0x1e4e, "Otildedieresis" },
                                                                { 0xf7f5, "Otildesmall" },
                                                                { 0x0050, "P" },
                                                                { 0x1e54, "Pacute" },
                                                                { 0x24c5, "Pcircle" },
                                                                { 0x1e56, "Pdotaccent" },
                                                                { 0x041f, "Pecyrillic" },
                                                                { 0x054a, "Peharmenian" },
                                                                { 0x04a6, "Pemiddlehookcyrillic" },
                                                                { 0x03a6, "Phi" },
                                                                { 0x01a4, "Phook" },
                                                                { 0x03a0, "Pi" },
                                                                { 0x0553, "Piwrarmenian" },
                                                                { 0xff30, "Pmonospace" },
                                                                { 0x03a8, "Psi" },
                                                                { 0x0470, "Psicyrillic" },
                                                                { 0xf770, "Psmall" },
                                                                { 0x0051, "Q" },
                                                                { 0x24c6, "Qcircle" },
                                                                { 0xff31, "Qmonospace" },
                                                                { 0xf771, "Qsmall" },
                                                                { 0x0052, "R" },
                                                                { 0x054c, "Raarmenian" },
                                                                { 0x0154, "Racute" },
                                                                { 0x0158, "Rcaron" },
                                                                { 0x0156, "Rcedilla" },
                                                                { 0x24c7, "Rcircle" },
                                                                { 0x0156, "Rcommaaccent" },
                                                                { 0x0210, "Rdblgrave" },
                                                                { 0x1e58, "Rdotaccent" },
                                                                { 0x1e5a, "Rdotbelow" },
                                                                { 0x1e5c, "Rdotbelowmacron" },
                                                                { 0x0550, "Reharmenian" },
                                                                { 0x211c, "Rfraktur" },
                                                                { 0x03a1, "Rho" },
                                                                { 0xf6fc, "Ringsmall" },
                                                                { 0x0212, "Rinvertedbreve" },
                                                                { 0x1e5e, "Rlinebelow" },
                                                                { 0xff32, "Rmonospace" },
                                                                { 0xf772, "Rsmall" },
                                                                { 0x0281, "Rsmallinverted" },
                                                                { 0x02b6, "Rsmallinvertedsuperior" },
                                                                { 0x0053, "S" },
                                                                { 0x250c, "SF010000" },
                                                                { 0x2514, "SF020000" },
                                                                { 0x2510, "SF030000" },
                                                                { 0x2518, "SF040000" },
                                                                { 0x253c, "SF050000" },
                                                                { 0x252c, "SF060000" },
                                                                { 0x2534, "SF070000" },
                                                                { 0x251c, "SF080000" },
                                                                { 0x2524, "SF090000" },
                                                                { 0x2500, "SF100000" },
                                                                { 0x2502, "SF110000" },
                                                                { 0x2561, "SF190000" },
                                                                { 0x2562, "SF200000" },
                                                                { 0x2556, "SF210000" },
                                                                { 0x2555, "SF220000" },
                                                                { 0x2563, "SF230000" },
                                                                { 0x2551, "SF240000" },
                                                                { 0x2557, "SF250000" },
                                                                { 0x255d, "SF260000" },
                                                                { 0x255c, "SF270000" },
                                                                { 0x255b, "SF280000" },
                                                                { 0x255e, "SF360000" },
                                                                { 0x255f, "SF370000" },
                                                                { 0x255a, "SF380000" },
                                                                { 0x2554, "SF390000" },
                                                                { 0x2569, "SF400000" },
                                                                { 0x2566, "SF410000" },
                                                                { 0x2560, "SF420000" },
                                                                { 0x2550, "SF430000" },
                                                                { 0x256c, "SF440000" },
                                                                { 0x2567, "SF450000" },
                                                                { 0x2568, "SF460000" },
                                                                { 0x2564, "SF470000" },
                                                                { 0x2565, "SF480000" },
                                                                { 0x2559, "SF490000" },
                                                                { 0x2558, "SF500000" },
                                                                { 0x2552, "SF510000" },
                                                                { 0x2553, "SF520000" },
                                                                { 0x256b, "SF530000" },
                                                                { 0x256a, "SF540000" },
                                                                { 0x015a, "Sacute" },
                                                                { 0x1e64, "Sacutedotaccent" },
                                                                { 0x03e0, "Sampigreek" },
                                                                { 0x0160, "Scaron" },
                                                                { 0x1e66, "Scarondotaccent" },
                                                                { 0xf6fd, "Scaronsmall" },
                                                                { 0x015e, "Scedilla" },
                                                                { 0x018f, "Schwa" },
                                                                { 0x04d8, "Schwacyrillic" },
                                                                { 0x04da, "Schwadieresiscyrillic" },
                                                                { 0x24c8, "Scircle" },
                                                                { 0x015c, "Scircumflex" },
                                                                { 0x0218, "Scommaaccent" },
                                                                { 0x1e60, "Sdotaccent" },
                                                                { 0x1e62, "Sdotbelow" },
                                                                { 0x1e68, "Sdotbelowdotaccent" },
                                                                { 0x054d, "Seharmenian" },
                                                                { 0x2166, "Sevenroman" },
                                                                { 0x0547, "Shaarmenian" },
                                                                { 0x0428, "Shacyrillic" },
                                                                { 0x0429, "Shchacyrillic" },
                                                                { 0x03e2, "Sheicoptic" },
                                                                { 0x04ba, "Shhacyrillic" },
                                                                { 0x03ec, "Shimacoptic" },
                                                                { 0x03a3, "Sigma" },
                                                                { 0x2165, "Sixroman" },
                                                                { 0xff33, "Smonospace" },
                                                                { 0x042c, "Softsigncyrillic" },
                                                                { 0xf773, "Ssmall" },
                                                                { 0x03da, "Stigmagreek" },
                                                                { 0x0054, "T" },
                                                                { 0x03a4, "Tau" },
                                                                { 0x0166, "Tbar" },
                                                                { 0x0164, "Tcaron" },
                                                                { 0x0162, "Tcedilla" },
                                                                { 0x24c9, "Tcircle" },
                                                                { 0x1e70, "Tcircumflexbelow" },
                                                                { 0x0162, "Tcommaaccent" },
                                                                { 0x1e6a, "Tdotaccent" },
                                                                { 0x1e6c, "Tdotbelow" },
                                                                { 0x0422, "Tecyrillic" },
                                                                { 0x04ac, "Tedescendercyrillic" },
                                                                { 0x2169, "Tenroman" },
                                                                { 0x04b4, "Tetsecyrillic" },
                                                                { 0x0398, "Theta" },
                                                                { 0x01ac, "Thook" },
                                                                { 0x00de, "Thorn" },
                                                                { 0xf7fe, "Thornsmall" },
                                                                { 0x2162, "Threeroman" },
                                                                { 0xf6fe, "Tildesmall" },
                                                                { 0x054f, "Tiwnarmenian" },
                                                                { 0x1e6e, "Tlinebelow" },
                                                                { 0xff34, "Tmonospace" },
                                                                { 0x0539, "Toarmenian" },
                                                                { 0x01bc, "Tonefive" },
                                                                { 0x0184, "Tonesix" },
                                                                { 0x01a7, "Tonetwo" },
                                                                { 0x01ae, "Tretroflexhook" },
                                                                { 0x0426, "Tsecyrillic" },
                                                                { 0x040b, "Tshecyrillic" },
                                                                { 0xf774, "Tsmall" },
                                                                { 0x216b, "Twelveroman" },
                                                                { 0x2161, "Tworoman" },
                                                                { 0x0055, "U" },
                                                                { 0x00da, "Uacute" },
                                                                { 0xf7fa, "Uacutesmall" },
                                                                { 0x016c, "Ubreve" },
                                                                { 0x01d3, "Ucaron" },
                                                                { 0x24ca, "Ucircle" },
                                                                { 0x00db, "Ucircumflex" },
                                                                { 0x1e76, "Ucircumflexbelow" },
                                                                { 0xf7fb, "Ucircumflexsmall" },
                                                                { 0x0423, "Ucyrillic" },
                                                                { 0x0170, "Udblacute" },
                                                                { 0x0214, "Udblgrave" },
                                                                { 0x00dc, "Udieresis" },
                                                                { 0x01d7, "Udieresisacute" },
                                                                { 0x1e72, "Udieresisbelow" },
                                                                { 0x01d9, "Udieresiscaron" },
                                                                { 0x04f0, "Udieresiscyrillic" },
                                                                { 0x01db, "Udieresisgrave" },
                                                                { 0x01d5, "Udieresismacron" },
                                                                { 0xf7fc, "Udieresissmall" },
                                                                { 0x1ee4, "Udotbelow" },
                                                                { 0x00d9, "Ugrave" },
                                                                { 0xf7f9, "Ugravesmall" },
                                                                { 0x1ee6, "Uhookabove" },
                                                                { 0x01af, "Uhorn" },
                                                                { 0x1ee8, "Uhornacute" },
                                                                { 0x1ef0, "Uhorndotbelow" },
                                                                { 0x1eea, "Uhorngrave" },
                                                                { 0x1eec, "Uhornhookabove" },
                                                                { 0x1eee, "Uhorntilde" },
                                                                { 0x0170, "Uhungarumlaut" },
                                                                { 0x04f2, "Uhungarumlautcyrillic" },
                                                                { 0x0216, "Uinvertedbreve" },
                                                                { 0x0478, "Ukcyrillic" },
                                                                { 0x016a, "Umacron" },
                                                                { 0x04ee, "Umacroncyrillic" },
                                                                { 0x1e7a, "Umacrondieresis" },
                                                                { 0xff35, "Umonospace" },
                                                                { 0x0172, "Uogonek" },
                                                                { 0x03a5, "Upsilon" },
                                                                { 0x03d2, "Upsilon1" },
                                                                { 0x03d3, "Upsilonacutehooksymbolgreek" },
                                                                { 0x01b1, "Upsilonafrican" },
                                                                { 0x03ab, "Upsilondieresis" },
                                                                { 0x03d4, "Upsilondieresishooksymbolgreek" },
                                                                { 0x03d2, "Upsilonhooksymbol" },
                                                                { 0x038e, "Upsilontonos" },
                                                                { 0x016e, "Uring" },
                                                                { 0x040e, "Ushortcyrillic" },
                                                                { 0xf775, "Usmall" },
                                                                { 0x04ae, "Ustraightcyrillic" },
                                                                { 0x04b0, "Ustraightstrokecyrillic" },
                                                                { 0x0168, "Utilde" },
                                                                { 0x1e78, "Utildeacute" },
                                                                { 0x1e74, "Utildebelow" },
                                                                { 0x0056, "V" },
                                                                { 0x24cb, "Vcircle" },
                                                                { 0x1e7e, "Vdotbelow" },
                                                                { 0x0412, "Vecyrillic" },
                                                                { 0x054e, "Vewarmenian" },
                                                                { 0x01b2, "Vhook" },
                                                                { 0xff36, "Vmonospace" },
                                                                { 0x0548, "Voarmenian" },
                                                                { 0xf776, "Vsmall" },
                                                                { 0x1e7c, "Vtilde" },
                                                                { 0x0057, "W" },
                                                                { 0x1e82, "Wacute" },
                                                                { 0x24cc, "Wcircle" },
                                                                { 0x0174, "Wcircumflex" },
                                                                { 0x1e84, "Wdieresis" },
                                                                { 0x1e86, "Wdotaccent" },
                                                                { 0x1e88, "Wdotbelow" },
                                                                { 0x1e80, "Wgrave" },
                                                                { 0xff37, "Wmonospace" },
                                                                { 0xf777, "Wsmall" },
                                                                { 0x0058, "X" },
                                                                { 0x24cd, "Xcircle" },
                                                                { 0x1e8c, "Xdieresis" },
                                                                { 0x1e8a, "Xdotaccent" },
                                                                { 0x053d, "Xeharmenian" },
                                                                { 0x039e, "Xi" },
                                                                { 0xff38, "Xmonospace" },
                                                                { 0xf778, "Xsmall" },
                                                                { 0x0059, "Y" },
                                                                { 0x00dd, "Yacute" },
                                                                { 0xf7fd, "Yacutesmall" },
                                                                { 0x0462, "Yatcyrillic" },
                                                                { 0x24ce, "Ycircle" },
                                                                { 0x0176, "Ycircumflex" },
                                                                { 0x0178, "Ydieresis" },
                                                                { 0xf7ff, "Ydieresissmall" },
                                                                { 0x1e8e, "Ydotaccent" },
                                                                { 0x1ef4, "Ydotbelow" },
                                                                { 0x042b, "Yericyrillic" },
                                                                { 0x04f8, "Yerudieresiscyrillic" },
                                                                { 0x1ef2, "Ygrave" },
                                                                { 0x01b3, "Yhook" },
                                                                { 0x1ef6, "Yhookabove" },
                                                                { 0x0545, "Yiarmenian" },
                                                                { 0x0407, "Yicyrillic" },
                                                                { 0x0552, "Yiwnarmenian" },
                                                                { 0xff39, "Ymonospace" },
                                                                { 0xf779, "Ysmall" },
                                                                { 0x1ef8, "Ytilde" },
                                                                { 0x046a, "Yusbigcyrillic" },
                                                                { 0x046c, "Yusbigiotifiedcyrillic" },
                                                                { 0x0466, "Yuslittlecyrillic" },
                                                                { 0x0468, "Yuslittleiotifiedcyrillic" },
                                                                { 0x005a, "Z" },
                                                                { 0x0536, "Zaarmenian" },
                                                                { 0x0179, "Zacute" },
                                                                { 0x017d, "Zcaron" },
                                                                { 0xf6ff, "Zcaronsmall" },
                                                                { 0x24cf, "Zcircle" },
                                                                { 0x1e90, "Zcircumflex" },
                                                                { 0x017b, "Zdot" },
                                                                { 0x017b, "Zdotaccent" },
                                                                { 0x1e92, "Zdotbelow" },
                                                                { 0x0417, "Zecyrillic" },
                                                                { 0x0498, "Zedescendercyrillic" },
                                                                { 0x04de, "Zedieresiscyrillic" },
                                                                { 0x0396, "Zeta" },
                                                                { 0x053a, "Zhearmenian" },
                                                                { 0x04c1, "Zhebrevecyrillic" },
                                                                { 0x0416, "Zhecyrillic" },
                                                                { 0x0496, "Zhedescendercyrillic" },
                                                                { 0x04dc, "Zhedieresiscyrillic" },
                                                                { 0x1e94, "Zlinebelow" },
                                                                { 0xff3a, "Zmonospace" },
                                                                { 0xf77a, "Zsmall" },
                                                                { 0x01b5, "Zstroke" },
                                                                { 0x0022, "\"" },
                                                                { 0x005c, "\\" },
                                                                { 0x005d, "]" },
                                                                { 0x005e, "^" },
                                                                { 0x005f, "_" },
                                                                { 0x0060, "`" },
                                                                { 0x0061, "a" },
                                                                { 0x0986, "aabengali" },
                                                                { 0x00e1, "aacute" },
                                                                { 0x0906, "aadeva" },
                                                                { 0x0a86, "aagujarati" },
                                                                { 0x0a06, "aagurmukhi" },
                                                                { 0x0a3e, "aamatragurmukhi" },
                                                                { 0x3303, "aarusquare" },
                                                                { 0x09be, "aavowelsignbengali" },
                                                                { 0x093e, "aavowelsigndeva" },
                                                                { 0x0abe, "aavowelsigngujarati" },
                                                                { 0x055f, "abbreviationmarkarmenian" },
                                                                { 0x0970, "abbreviationsigndeva" },
                                                                { 0x0985, "abengali" },
                                                                { 0x311a, "abopomofo" },
                                                                { 0x0103, "abreve" },
                                                                { 0x1eaf, "abreveacute" },
                                                                { 0x04d1, "abrevecyrillic" },
                                                                { 0x1eb7, "abrevedotbelow" },
                                                                { 0x1eb1, "abrevegrave" },
                                                                { 0x1eb3, "abrevehookabove" },
                                                                { 0x1eb5, "abrevetilde" },
                                                                { 0x01ce, "acaron" },
                                                                { 0x24d0, "acircle" },
                                                                { 0x00e2, "acircumflex" },
                                                                { 0x1ea5, "acircumflexacute" },
                                                                { 0x1ead, "acircumflexdotbelow" },
                                                                { 0x1ea7, "acircumflexgrave" },
                                                                { 0x1ea9, "acircumflexhookabove" },
                                                                { 0x1eab, "acircumflextilde" },
                                                                { 0x00b4, "acute" },
                                                                { 0x0317, "acutebelowcmb" },
                                                                { 0x0301, "acutecmb" },
                                                                { 0x0301, "acutecomb" },
                                                                { 0x0954, "acutedeva" },
                                                                { 0x02cf, "acutelowmod" },
                                                                { 0x0341, "acutetonecmb" },
                                                                { 0x0430, "acyrillic" },
                                                                { 0x0201, "adblgrave" },
                                                                { 0x0a71, "addakgurmukhi" },
                                                                { 0x0905, "adeva" },
                                                                { 0x00e4, "adieresis" },
                                                                { 0x04d3, "adieresiscyrillic" },
                                                                { 0x01df, "adieresismacron" },
                                                                { 0x1ea1, "adotbelow" },
                                                                { 0x01e1, "adotmacron" },
                                                                { 0x00e6, "ae" },
                                                                { 0x01fd, "aeacute" },
                                                                { 0x3150, "aekorean" },
                                                                { 0x01e3, "aemacron" },
                                                                { 0x2015, "afii00208" },
                                                                { 0x20a4, "afii08941" },
                                                                { 0x0410, "afii10017" },
                                                                { 0x0411, "afii10018" },
                                                                { 0x0412, "afii10019" },
                                                                { 0x0413, "afii10020" },
                                                                { 0x0414, "afii10021" },
                                                                { 0x0415, "afii10022" },
                                                                { 0x0401, "afii10023" },
                                                                { 0x0416, "afii10024" },
                                                                { 0x0417, "afii10025" },
                                                                { 0x0418, "afii10026" },
                                                                { 0x0419, "afii10027" },
                                                                { 0x041a, "afii10028" },
                                                                { 0x041b, "afii10029" },
                                                                { 0x041c, "afii10030" },
                                                                { 0x041d, "afii10031" },
                                                                { 0x041e, "afii10032" },
                                                                { 0x041f, "afii10033" },
                                                                { 0x0420, "afii10034" },
                                                                { 0x0421, "afii10035" },
                                                                { 0x0422, "afii10036" },
                                                                { 0x0423, "afii10037" },
                                                                { 0x0424, "afii10038" },
                                                                { 0x0425, "afii10039" },
                                                                { 0x0426, "afii10040" },
                                                                { 0x0427, "afii10041" },
                                                                { 0x0428, "afii10042" },
                                                                { 0x0429, "afii10043" },
                                                                { 0x042a, "afii10044" },
                                                                { 0x042b, "afii10045" },
                                                                { 0x042c, "afii10046" },
                                                                { 0x042d, "afii10047" },
                                                                { 0x042e, "afii10048" },
                                                                { 0x042f, "afii10049" },
                                                                { 0x0490, "afii10050" },
                                                                { 0x0402, "afii10051" },
                                                                { 0x0403, "afii10052" },
                                                                { 0x0404, "afii10053" },
                                                                { 0x0405, "afii10054" },
                                                                { 0x0406, "afii10055" },
                                                                { 0x0407, "afii10056" },
                                                                { 0x0408, "afii10057" },
                                                                { 0x0409, "afii10058" },
                                                                { 0x040a, "afii10059" },
                                                                { 0x040b, "afii10060" },
                                                                { 0x040c, "afii10061" },
                                                                { 0x040e, "afii10062" },
                                                                { 0xf6c4, "afii10063" },
                                                                { 0xf6c5, "afii10064" },
                                                                { 0x0430, "afii10065" },
                                                                { 0x0431, "afii10066" },
                                                                { 0x0432, "afii10067" },
                                                                { 0x0433, "afii10068" },
                                                                { 0x0434, "afii10069" },
                                                                { 0x0435, "afii10070" },
                                                                { 0x0451, "afii10071" },
                                                                { 0x0436, "afii10072" },
                                                                { 0x0437, "afii10073" },
                                                                { 0x0438, "afii10074" },
                                                                { 0x0439, "afii10075" },
                                                                { 0x043a, "afii10076" },
                                                                { 0x043b, "afii10077" },
                                                                { 0x043c, "afii10078" },
                                                                { 0x043d, "afii10079" },
                                                                { 0x043e, "afii10080" },
                                                                { 0x043f, "afii10081" },
                                                                { 0x0440, "afii10082" },
                                                                { 0x0441, "afii10083" },
                                                                { 0x0442, "afii10084" },
                                                                { 0x0443, "afii10085" },
                                                                { 0x0444, "afii10086" },
                                                                { 0x0445, "afii10087" },
                                                                { 0x0446, "afii10088" },
                                                                { 0x0447, "afii10089" },
                                                                { 0x0448, "afii10090" },
                                                                { 0x0449, "afii10091" },
                                                                { 0x044a, "afii10092" },
                                                                { 0x044b, "afii10093" },
                                                                { 0x044c, "afii10094" },
                                                                { 0x044d, "afii10095" },
                                                                { 0x044e, "afii10096" },
                                                                { 0x044f, "afii10097" },
                                                                { 0x0491, "afii10098" },
                                                                { 0x0452, "afii10099" },
                                                                { 0x0453, "afii10100" },
                                                                { 0x0454, "afii10101" },
                                                                { 0x0455, "afii10102" },
                                                                { 0x0456, "afii10103" },
                                                                { 0x0457, "afii10104" },
                                                                { 0x0458, "afii10105" },
                                                                { 0x0459, "afii10106" },
                                                                { 0x045a, "afii10107" },
                                                                { 0x045b, "afii10108" },
                                                                { 0x045c, "afii10109" },
                                                                { 0x045e, "afii10110" },
                                                                { 0x040f, "afii10145" },
                                                                { 0x0462, "afii10146" },
                                                                { 0x0472, "afii10147" },
                                                                { 0x0474, "afii10148" },
                                                                { 0xf6c6, "afii10192" },
                                                                { 0x045f, "afii10193" },
                                                                { 0x0463, "afii10194" },
                                                                { 0x0473, "afii10195" },
                                                                { 0x0475, "afii10196" },
                                                                { 0xf6c7, "afii10831" },
                                                                { 0xf6c8, "afii10832" },
                                                                { 0x04d9, "afii10846" },
                                                                { 0x200e, "afii299" },
                                                                { 0x200f, "afii300" },
                                                                { 0x200d, "afii301" },
                                                                { 0x066a, "afii57381" },
                                                                { 0x060c, "afii57388" },
                                                                { 0x0660, "afii57392" },
                                                                { 0x0661, "afii57393" },
                                                                { 0x0662, "afii57394" },
                                                                { 0x0663, "afii57395" },
                                                                { 0x0664, "afii57396" },
                                                                { 0x0665, "afii57397" },
                                                                { 0x0666, "afii57398" },
                                                                { 0x0667, "afii57399" },
                                                                { 0x0668, "afii57400" },
                                                                { 0x0669, "afii57401" },
                                                                { 0x061b, "afii57403" },
                                                                { 0x061f, "afii57407" },
                                                                { 0x0621, "afii57409" },
                                                                { 0x0622, "afii57410" },
                                                                { 0x0623, "afii57411" },
                                                                { 0x0624, "afii57412" },
                                                                { 0x0625, "afii57413" },
                                                                { 0x0626, "afii57414" },
                                                                { 0x0627, "afii57415" },
                                                                { 0x0628, "afii57416" },
                                                                { 0x0629, "afii57417" },
                                                                { 0x062a, "afii57418" },
                                                                { 0x062b, "afii57419" },
                                                                { 0x062c, "afii57420" },
                                                                { 0x062d, "afii57421" },
                                                                { 0x062e, "afii57422" },
                                                                { 0x062f, "afii57423" },
                                                                { 0x0630, "afii57424" },
                                                                { 0x0631, "afii57425" },
                                                                { 0x0632, "afii57426" },
                                                                { 0x0633, "afii57427" },
                                                                { 0x0634, "afii57428" },
                                                                { 0x0635, "afii57429" },
                                                                { 0x0636, "afii57430" },
                                                                { 0x0637, "afii57431" },
                                                                { 0x0638, "afii57432" },
                                                                { 0x0639, "afii57433" },
                                                                { 0x063a, "afii57434" },
                                                                { 0x0640, "afii57440" },
                                                                { 0x0641, "afii57441" },
                                                                { 0x0642, "afii57442" },
                                                                { 0x0643, "afii57443" },
                                                                { 0x0644, "afii57444" },
                                                                { 0x0645, "afii57445" },
                                                                { 0x0646, "afii57446" },
                                                                { 0x0648, "afii57448" },
                                                                { 0x0649, "afii57449" },
                                                                { 0x064a, "afii57450" },
                                                                { 0x064b, "afii57451" },
                                                                { 0x064c, "afii57452" },
                                                                { 0x064d, "afii57453" },
                                                                { 0x064e, "afii57454" },
                                                                { 0x064f, "afii57455" },
                                                                { 0x0650, "afii57456" },
                                                                { 0x0651, "afii57457" },
                                                                { 0x0652, "afii57458" },
                                                                { 0x0647, "afii57470" },
                                                                { 0x06a4, "afii57505" },
                                                                { 0x067e, "afii57506" },
                                                                { 0x0686, "afii57507" },
                                                                { 0x0698, "afii57508" },
                                                                { 0x06af, "afii57509" },
                                                                { 0x0679, "afii57511" },
                                                                { 0x0688, "afii57512" },
                                                                { 0x0691, "afii57513" },
                                                                { 0x06ba, "afii57514" },
                                                                { 0x06d2, "afii57519" },
                                                                { 0x06d5, "afii57534" },
                                                                { 0x20aa, "afii57636" },
                                                                { 0x05be, "afii57645" },
                                                                { 0x05c3, "afii57658" },
                                                                { 0x05d0, "afii57664" },
                                                                { 0x05d1, "afii57665" },
                                                                { 0x05d2, "afii57666" },
                                                                { 0x05d3, "afii57667" },
                                                                { 0x05d4, "afii57668" },
                                                                { 0x05d5, "afii57669" },
                                                                { 0x05d6, "afii57670" },
                                                                { 0x05d7, "afii57671" },
                                                                { 0x05d8, "afii57672" },
                                                                { 0x05d9, "afii57673" },
                                                                { 0x05da, "afii57674" },
                                                                { 0x05db, "afii57675" },
                                                                { 0x05dc, "afii57676" },
                                                                { 0x05dd, "afii57677" },
                                                                { 0x05de, "afii57678" },
                                                                { 0x05df, "afii57679" },
                                                                { 0x05e0, "afii57680" },
                                                                { 0x05e1, "afii57681" },
                                                                { 0x05e2, "afii57682" },
                                                                { 0x05e3, "afii57683" },
                                                                { 0x05e4, "afii57684" },
                                                                { 0x05e5, "afii57685" },
                                                                { 0x05e6, "afii57686" },
                                                                { 0x05e7, "afii57687" },
                                                                { 0x05e8, "afii57688" },
                                                                { 0x05e9, "afii57689" },
                                                                { 0x05ea, "afii57690" },
                                                                { 0xfb2a, "afii57694" },
                                                                { 0xfb2b, "afii57695" },
                                                                { 0xfb4b, "afii57700" },
                                                                { 0xfb1f, "afii57705" },
                                                                { 0x05f0, "afii57716" },
                                                                { 0x05f1, "afii57717" },
                                                                { 0x05f2, "afii57718" },
                                                                { 0xfb35, "afii57723" },
                                                                { 0x05b4, "afii57793" },
                                                                { 0x05b5, "afii57794" },
                                                                { 0x05b6, "afii57795" },
                                                                { 0x05bb, "afii57796" },
                                                                { 0x05b8, "afii57797" },
                                                                { 0x05b7, "afii57798" },
                                                                { 0x05b0, "afii57799" },
                                                                { 0x05b2, "afii57800" },
                                                                { 0x05b1, "afii57801" },
                                                                { 0x05b3, "afii57802" },
                                                                { 0x05c2, "afii57803" },
                                                                { 0x05c1, "afii57804" },
                                                                { 0x05b9, "afii57806" },
                                                                { 0x05bc, "afii57807" },
                                                                { 0x05bd, "afii57839" },
                                                                { 0x05bf, "afii57841" },
                                                                { 0x05c0, "afii57842" },
                                                                { 0x02bc, "afii57929" },
                                                                { 0x2105, "afii61248" },
                                                                { 0x2113, "afii61289" },
                                                                { 0x2116, "afii61352" },
                                                                { 0x202c, "afii61573" },
                                                                { 0x202d, "afii61574" },
                                                                { 0x202e, "afii61575" },
                                                                { 0x200c, "afii61664" },
                                                                { 0x066d, "afii63167" },
                                                                { 0x02bd, "afii64937" },
                                                                { 0x00e0, "agrave" },
                                                                { 0x0a85, "agujarati" },
                                                                { 0x0a05, "agurmukhi" },
                                                                { 0x3042, "ahiragana" },
                                                                { 0x1ea3, "ahookabove" },
                                                                { 0x0990, "aibengali" },
                                                                { 0x311e, "aibopomofo" },
                                                                { 0x0910, "aideva" },
                                                                { 0x04d5, "aiecyrillic" },
                                                                { 0x0a90, "aigujarati" },
                                                                { 0x0a10, "aigurmukhi" },
                                                                { 0x0a48, "aimatragurmukhi" },
                                                                { 0x0639, "ainarabic" },
                                                                { 0xfeca, "ainfinalarabic" },
                                                                { 0xfecb, "aininitialarabic" },
                                                                { 0xfecc, "ainmedialarabic" },
                                                                { 0x0203, "ainvertedbreve" },
                                                                { 0x09c8, "aivowelsignbengali" },
                                                                { 0x0948, "aivowelsigndeva" },
                                                                { 0x0ac8, "aivowelsigngujarati" },
                                                                { 0x30a2, "akatakana" },
                                                                { 0xff71, "akatakanahalfwidth" },
                                                                { 0x314f, "akorean" },
                                                                { 0x05d0, "alef" },
                                                                { 0x0627, "alefarabic" },
                                                                { 0xfb30, "alefdageshhebrew" },
                                                                { 0xfe8e, "aleffinalarabic" },
                                                                { 0x0623, "alefhamzaabovearabic" },
                                                                { 0xfe84, "alefhamzaabovefinalarabic" },
                                                                { 0x0625, "alefhamzabelowarabic" },
                                                                { 0xfe88, "alefhamzabelowfinalarabic" },
                                                                { 0x05d0, "alefhebrew" },
                                                                { 0xfb4f, "aleflamedhebrew" },
                                                                { 0x0622, "alefmaddaabovearabic" },
                                                                { 0xfe82, "alefmaddaabovefinalarabic" },
                                                                { 0x0649, "alefmaksuraarabic" },
                                                                { 0xfef0, "alefmaksurafinalarabic" },
                                                                { 0xfef3, "alefmaksurainitialarabic" },
                                                                { 0xfef4, "alefmaksuramedialarabic" },
                                                                { 0xfb2e, "alefpatahhebrew" },
                                                                { 0xfb2f, "alefqamatshebrew" },
                                                                { 0x2135, "aleph" },
                                                                { 0x224c, "allequal" },
                                                                { 0x03b1, "alpha" },
                                                                { 0x03ac, "alphatonos" },
                                                                { 0x0101, "amacron" },
                                                                { 0xff41, "amonospace" },
                                                                { 0x0026, "ampersand" },
                                                                { 0xff06, "ampersandmonospace" },
                                                                { 0xf726, "ampersandsmall" },
                                                                { 0x33c2, "amsquare" },
                                                                { 0x3122, "anbopomofo" },
                                                                { 0x3124, "angbopomofo" },
                                                                { 0x0e5a, "angkhankhuthai" },
                                                                { 0x2220, "angle" },
                                                                { 0x3008, "anglebracketleft" },
                                                                { 0xfe3f, "anglebracketleftvertical" },
                                                                { 0x3009, "anglebracketright" },
                                                                { 0xfe40, "anglebracketrightvertical" },
                                                                { 0x2329, "angleleft" },
                                                                { 0x232a, "angleright" },
                                                                { 0x212b, "angstrom" },
                                                                { 0x0387, "anoteleia" },
                                                                { 0x0952, "anudattadeva" },
                                                                { 0x0982, "anusvarabengali" },
                                                                { 0x0902, "anusvaradeva" },
                                                                { 0x0a82, "anusvaragujarati" },
                                                                { 0x0105, "aogonek" },
                                                                { 0x3300, "apaatosquare" },
                                                                { 0x249c, "aparen" },
                                                                { 0x055a, "apostrophearmenian" },
                                                                { 0x02bc, "apostrophemod" },
                                                                { 0xf8ff, "apple" },
                                                                { 0x2250, "approaches" },
                                                                { 0x2248, "approxequal" },
                                                                { 0x2252, "approxequalorimage" },
                                                                { 0x2245, "approximatelyequal" },
                                                                { 0x318e, "araeaekorean" },
                                                                { 0x318d, "araeakorean" },
                                                                { 0x2312, "arc" },
                                                                { 0x1e9a, "arighthalfring" },
                                                                { 0x00e5, "aring" },
                                                                { 0x01fb, "aringacute" },
                                                                { 0x1e01, "aringbelow" },
                                                                { 0x2194, "arrowboth" },
                                                                { 0x21e3, "arrowdashdown" },
                                                                { 0x21e0, "arrowdashleft" },
                                                                { 0x21e2, "arrowdashright" },
                                                                { 0x21e1, "arrowdashup" },
                                                                { 0x21d4, "arrowdblboth" },
                                                                { 0x21d3, "arrowdbldown" },
                                                                { 0x21d0, "arrowdblleft" },
                                                                { 0x21d2, "arrowdblright" },
                                                                { 0x21d1, "arrowdblup" },
                                                                { 0x2193, "arrowdown" },
                                                                { 0x2199, "arrowdownleft" },
                                                                { 0x2198, "arrowdownright" },
                                                                { 0x21e9, "arrowdownwhite" },
                                                                { 0x02c5, "arrowheaddownmod" },
                                                                { 0x02c2, "arrowheadleftmod" },
                                                                { 0x02c3, "arrowheadrightmod" },
                                                                { 0x02c4, "arrowheadupmod" },
                                                                { 0xf8e7, "arrowhorizex" },
                                                                { 0x2190, "arrowleft" },
                                                                { 0x21d0, "arrowleftdbl" },
                                                                { 0x21cd, "arrowleftdblstroke" },
                                                                { 0x21c6, "arrowleftoverright" },
                                                                { 0x21e6, "arrowleftwhite" },
                                                                { 0x2192, "arrowright" },
                                                                { 0x21cf, "arrowrightdblstroke" },
                                                                { 0x279e, "arrowrightheavy" },
                                                                { 0x21c4, "arrowrightoverleft" },
                                                                { 0x21e8, "arrowrightwhite" },
                                                                { 0x21e4, "arrowtableft" },
                                                                { 0x21e5, "arrowtabright" },
                                                                { 0x2191, "arrowup" },
                                                                { 0x2195, "arrowupdn" },
                                                                { 0x21a8, "arrowupdnbse" },
                                                                { 0x21a8, "arrowupdownbase" },
                                                                { 0x2196, "arrowupleft" },
                                                                { 0x21c5, "arrowupleftofdown" },
                                                                { 0x2197, "arrowupright" },
                                                                { 0x21e7, "arrowupwhite" },
                                                                { 0xf8e6, "arrowvertex" },
                                                                { 0x005e, "asciicircum" },
                                                                { 0xff3e, "asciicircummonospace" },
                                                                { 0x007e, "asciitilde" },
                                                                { 0xff5e, "asciitildemonospace" },
                                                                { 0x0251, "ascript" },
                                                                { 0x0252, "ascriptturned" },
                                                                { 0x3041, "asmallhiragana" },
                                                                { 0x30a1, "asmallkatakana" },
                                                                { 0xff67, "asmallkatakanahalfwidth" },
                                                                { 0x002a, "asterisk" },
                                                                { 0x066d, "asteriskaltonearabic" },
                                                                { 0x066d, "asteriskarabic" },
                                                                { 0x2217, "asteriskmath" },
                                                                { 0xff0a, "asteriskmonospace" },
                                                                { 0xfe61, "asterisksmall" },
                                                                { 0x2042, "asterism" },
                                                                { 0xf6e9, "asuperior" },
                                                                { 0x2243, "asymptoticallyequal" },
                                                                { 0x0040, "at" },
                                                                { 0x00e3, "atilde" },
                                                                { 0xff20, "atmonospace" },
                                                                { 0xfe6b, "atsmall" },
                                                                { 0x0250, "aturned" },
                                                                { 0x0994, "aubengali" },
                                                                { 0x3120, "aubopomofo" },
                                                                { 0x0914, "audeva" },
                                                                { 0x0a94, "augujarati" },
                                                                { 0x0a14, "augurmukhi" },
                                                                { 0x09d7, "aulengthmarkbengali" },
                                                                { 0x0a4c, "aumatragurmukhi" },
                                                                { 0x09cc, "auvowelsignbengali" },
                                                                { 0x094c, "auvowelsigndeva" },
                                                                { 0x0acc, "auvowelsigngujarati" },
                                                                { 0x093d, "avagrahadeva" },
                                                                { 0x0561, "aybarmenian" },
                                                                { 0x05e2, "ayin" },
                                                                { 0xfb20, "ayinaltonehebrew" },
                                                                { 0x05e2, "ayinhebrew" },
                                                                { 0x0062, "b" },
                                                                { 0x09ac, "babengali" },
                                                                { 0x005c, "backslash" },
                                                                { 0xff3c, "backslashmonospace" },
                                                                { 0x092c, "badeva" },
                                                                { 0x0aac, "bagujarati" },
                                                                { 0x0a2c, "bagurmukhi" },
                                                                { 0x3070, "bahiragana" },
                                                                { 0x0e3f, "bahtthai" },
                                                                { 0x30d0, "bakatakana" },
                                                                { 0x007c, "bar" },
                                                                { 0xff5c, "barmonospace" },
                                                                { 0x3105, "bbopomofo" },
                                                                { 0x24d1, "bcircle" },
                                                                { 0x1e03, "bdotaccent" },
                                                                { 0x1e05, "bdotbelow" },
                                                                { 0x266c, "beamedsixteenthnotes" },
                                                                { 0x2235, "because" },
                                                                { 0x0431, "becyrillic" },
                                                                { 0x0628, "beharabic" },
                                                                { 0xfe90, "behfinalarabic" },
                                                                { 0xfe91, "behinitialarabic" },
                                                                { 0x3079, "behiragana" },
                                                                { 0xfe92, "behmedialarabic" },
                                                                { 0xfc9f, "behmeeminitialarabic" },
                                                                { 0xfc08, "behmeemisolatedarabic" },
                                                                { 0xfc6d, "behnoonfinalarabic" },
                                                                { 0x30d9, "bekatakana" },
                                                                { 0x0562, "benarmenian" },
                                                                { 0x05d1, "bet" },
                                                                { 0x03b2, "beta" },
                                                                { 0x03d0, "betasymbolgreek" },
                                                                { 0xfb31, "betdagesh" },
                                                                { 0xfb31, "betdageshhebrew" },
                                                                { 0x05d1, "bethebrew" },
                                                                { 0xfb4c, "betrafehebrew" },
                                                                { 0x09ad, "bhabengali" },
                                                                { 0x092d, "bhadeva" },
                                                                { 0x0aad, "bhagujarati" },
                                                                { 0x0a2d, "bhagurmukhi" },
                                                                { 0x0253, "bhook" },
                                                                { 0x3073, "bihiragana" },
                                                                { 0x30d3, "bikatakana" },
                                                                { 0x0298, "bilabialclick" },
                                                                { 0x0a02, "bindigurmukhi" },
                                                                { 0x3331, "birusquare" },
                                                                { 0x25cf, "blackcircle" },
                                                                { 0x25c6, "blackdiamond" },
                                                                { 0x25bc, "blackdownpointingtriangle" },
                                                                { 0x25c4, "blackleftpointingpointer" },
                                                                { 0x25c0, "blackleftpointingtriangle" },
                                                                { 0x3010, "blacklenticularbracketleft" },
                                                                { 0xfe3b, "blacklenticularbracketleftvertical" },
                                                                { 0x3011, "blacklenticularbracketright" },
                                                                { 0xfe3c, "blacklenticularbracketrightvertical" },
                                                                { 0x25e3, "blacklowerlefttriangle" },
                                                                { 0x25e2, "blacklowerrighttriangle" },
                                                                { 0x25ac, "blackrectangle" },
                                                                { 0x25ba, "blackrightpointingpointer" },
                                                                { 0x25b6, "blackrightpointingtriangle" },
                                                                { 0x25aa, "blacksmallsquare" },
                                                                { 0x263b, "blacksmilingface" },
                                                                { 0x25a0, "blacksquare" },
                                                                { 0x2605, "blackstar" },
                                                                { 0x25e4, "blackupperlefttriangle" },
                                                                { 0x25e5, "blackupperrighttriangle" },
                                                                { 0x25b4, "blackuppointingsmalltriangle" },
                                                                { 0x25b2, "blackuppointingtriangle" },
                                                                { 0x2423, "blank" },
                                                                { 0x1e07, "blinebelow" },
                                                                { 0x2588, "block" },
                                                                { 0xff42, "bmonospace" },
                                                                { 0x0e1a, "bobaimaithai" },
                                                                { 0x307c, "bohiragana" },
                                                                { 0x30dc, "bokatakana" },
                                                                { 0x249d, "bparen" },
                                                                { 0x33c3, "bqsquare" },
                                                                { 0xf8f4, "braceex" },
                                                                { 0x007b, "braceleft" },
                                                                { 0xf8f3, "braceleftbt" },
                                                                { 0xf8f2, "braceleftmid" },
                                                                { 0xff5b, "braceleftmonospace" },
                                                                { 0xfe5b, "braceleftsmall" },
                                                                { 0xf8f1, "bracelefttp" },
                                                                { 0xfe37, "braceleftvertical" },
                                                                { 0x007d, "braceright" },
                                                                { 0xf8fe, "bracerightbt" },
                                                                { 0xf8fd, "bracerightmid" },
                                                                { 0xff5d, "bracerightmonospace" },
                                                                { 0xfe5c, "bracerightsmall" },
                                                                { 0xf8fc, "bracerighttp" },
                                                                { 0xfe38, "bracerightvertical" },
                                                                { 0x005b, "bracketleft" },
                                                                { 0xf8f0, "bracketleftbt" },
                                                                { 0xf8ef, "bracketleftex" },
                                                                { 0xff3b, "bracketleftmonospace" },
                                                                { 0xf8ee, "bracketlefttp" },
                                                                { 0x005d, "bracketright" },
                                                                { 0xf8fb, "bracketrightbt" },
                                                                { 0xf8fa, "bracketrightex" },
                                                                { 0xff3d, "bracketrightmonospace" },
                                                                { 0xf8f9, "bracketrighttp" },
                                                                { 0x02d8, "breve" },
                                                                { 0x032e, "brevebelowcmb" },
                                                                { 0x0306, "brevecmb" },
                                                                { 0x032f, "breveinvertedbelowcmb" },
                                                                { 0x0311, "breveinvertedcmb" },
                                                                { 0x0361, "breveinverteddoublecmb" },
                                                                { 0x032a, "bridgebelowcmb" },
                                                                { 0x033a, "bridgeinvertedbelowcmb" },
                                                                { 0x00a6, "brokenbar" },
                                                                { 0x0180, "bstroke" },
                                                                { 0xf6ea, "bsuperior" },
                                                                { 0x0183, "btopbar" },
                                                                { 0x3076, "buhiragana" },
                                                                { 0x30d6, "bukatakana" },
                                                                { 0x2022, "bullet" },
                                                                { 0x25d8, "bulletinverse" },
                                                                { 0x2219, "bulletoperator" },
                                                                { 0x25ce, "bullseye" },
                                                                { 0x0063, "c" },
                                                                { 0x056e, "caarmenian" },
                                                                { 0x099a, "cabengali" },
                                                                { 0x0107, "cacute" },
                                                                { 0x091a, "cadeva" },
                                                                { 0x0a9a, "cagujarati" },
                                                                { 0x0a1a, "cagurmukhi" },
                                                                { 0x3388, "calsquare" },
                                                                { 0x0981, "candrabindubengali" },
                                                                { 0x0310, "candrabinducmb" },
                                                                { 0x0901, "candrabindudeva" },
                                                                { 0x0a81, "candrabindugujarati" },
                                                                { 0x21ea, "capslock" },
                                                                { 0x2105, "careof" },
                                                                { 0x02c7, "caron" },
                                                                { 0x032c, "caronbelowcmb" },
                                                                { 0x030c, "caroncmb" },
                                                                { 0x21b5, "carriagereturn" },
                                                                { 0x3118, "cbopomofo" },
                                                                { 0x010d, "ccaron" },
                                                                { 0x00e7, "ccedilla" },
                                                                { 0x1e09, "ccedillaacute" },
                                                                { 0x24d2, "ccircle" },
                                                                { 0x0109, "ccircumflex" },
                                                                { 0x0255, "ccurl" },
                                                                { 0x010b, "cdot" },
                                                                { 0x010b, "cdotaccent" },
                                                                { 0x33c5, "cdsquare" },
                                                                { 0x00b8, "cedilla" },
                                                                { 0x0327, "cedillacmb" },
                                                                { 0x00a2, "cent" },
                                                                { 0x2103, "centigrade" },
                                                                { 0xf6df, "centinferior" },
                                                                { 0xffe0, "centmonospace" },
                                                                { 0xf7a2, "centoldstyle" },
                                                                { 0xf6e0, "centsuperior" },
                                                                { 0x0579, "chaarmenian" },
                                                                { 0x099b, "chabengali" },
                                                                { 0x091b, "chadeva" },
                                                                { 0x0a9b, "chagujarati" },
                                                                { 0x0a1b, "chagurmukhi" },
                                                                { 0x3114, "chbopomofo" },
                                                                { 0x04bd, "cheabkhasiancyrillic" },
                                                                { 0x2713, "checkmark" },
                                                                { 0x0447, "checyrillic" },
                                                                { 0x04bf, "chedescenderabkhasiancyrillic" },
                                                                { 0x04b7, "chedescendercyrillic" },
                                                                { 0x04f5, "chedieresiscyrillic" },
                                                                { 0x0573, "cheharmenian" },
                                                                { 0x04cc, "chekhakassiancyrillic" },
                                                                { 0x04b9, "cheverticalstrokecyrillic" },
                                                                { 0x03c7, "chi" },
                                                                { 0x3277, "chieuchacirclekorean" },
                                                                { 0x3217, "chieuchaparenkorean" },
                                                                { 0x3269, "chieuchcirclekorean" },
                                                                { 0x314a, "chieuchkorean" },
                                                                { 0x3209, "chieuchparenkorean" },
                                                                { 0x0e0a, "chochangthai" },
                                                                { 0x0e08, "chochanthai" },
                                                                { 0x0e09, "chochingthai" },
                                                                { 0x0e0c, "chochoethai" },
                                                                { 0x0188, "chook" },
                                                                { 0x3276, "cieucacirclekorean" },
                                                                { 0x3216, "cieucaparenkorean" },
                                                                { 0x3268, "cieuccirclekorean" },
                                                                { 0x3148, "cieuckorean" },
                                                                { 0x3208, "cieucparenkorean" },
                                                                { 0x321c, "cieucuparenkorean" },
                                                                { 0x25cb, "circle" },
                                                                { 0x2297, "circlemultiply" },
                                                                { 0x2299, "circleot" },
                                                                { 0x2295, "circleplus" },
                                                                { 0x3036, "circlepostalmark" },
                                                                { 0x25d0, "circlewithlefthalfblack" },
                                                                { 0x25d1, "circlewithrighthalfblack" },
                                                                { 0x02c6, "circumflex" },
                                                                { 0x032d, "circumflexbelowcmb" },
                                                                { 0x0302, "circumflexcmb" },
                                                                { 0x2327, "clear" },
                                                                { 0x01c2, "clickalveolar" },
                                                                { 0x01c0, "clickdental" },
                                                                { 0x01c1, "clicklateral" },
                                                                { 0x01c3, "clickretroflex" },
                                                                { 0x2663, "club" },
                                                                { 0x2663, "clubsuitblack" },
                                                                { 0x2667, "clubsuitwhite" },
                                                                { 0x33a4, "cmcubedsquare" },
                                                                { 0xff43, "cmonospace" },
                                                                { 0x33a0, "cmsquaredsquare" },
                                                                { 0x0581, "coarmenian" },
                                                                { 0x003a, "colon" },
                                                                { 0x20a1, "colonmonetary" },
                                                                { 0xff1a, "colonmonospace" },
                                                                { 0x20a1, "colonsign" },
                                                                { 0xfe55, "colonsmall" },
                                                                { 0x02d1, "colontriangularhalfmod" },
                                                                { 0x02d0, "colontriangularmod" },
                                                                { 0x002c, "comma" },
                                                                { 0x0313, "commaabovecmb" },
                                                                { 0x0315, "commaaboverightcmb" },
                                                                { 0xf6c3, "commaaccent" },
                                                                { 0x060c, "commaarabic" },
                                                                { 0x055d, "commaarmenian" },
                                                                { 0xf6e1, "commainferior" },
                                                                { 0xff0c, "commamonospace" },
                                                                { 0x0314, "commareversedabovecmb" },
                                                                { 0x02bd, "commareversedmod" },
                                                                { 0xfe50, "commasmall" },
                                                                { 0xf6e2, "commasuperior" },
                                                                { 0x0312, "commaturnedabovecmb" },
                                                                { 0x02bb, "commaturnedmod" },
                                                                { 0x263c, "compass" },
                                                                { 0x2245, "congruent" },
                                                                { 0x222e, "contourintegral" },
                                                                { 0x2303, "control" },
                                                                { 0x0006, "controlACK" },
                                                                { 0x0007, "controlBEL" },
                                                                { 0x0008, "controlBS" },
                                                                { 0x0018, "controlCAN" },
                                                                { 0x000d, "controlCR" },
                                                                { 0x0011, "controlDC1" },
                                                                { 0x0012, "controlDC2" },
                                                                { 0x0013, "controlDC3" },
                                                                { 0x0014, "controlDC4" },
                                                                { 0x007f, "controlDEL" },
                                                                { 0x0010, "controlDLE" },
                                                                { 0x0019, "controlEM" },
                                                                { 0x0005, "controlENQ" },
                                                                { 0x0004, "controlEOT" },
                                                                { 0x001b, "controlESC" },
                                                                { 0x0017, "controlETB" },
                                                                { 0x0003, "controlETX" },
                                                                { 0x000c, "controlFF" },
                                                                { 0x001c, "controlFS" },
                                                                { 0x001d, "controlGS" },
                                                                { 0x0009, "controlHT" },
                                                                { 0x000a, "controlLF" },
                                                                { 0x0015, "controlNAK" },
                                                                { 0x001e, "controlRS" },
                                                                { 0x000f, "controlSI" },
                                                                { 0x000e, "controlSO" },
                                                                { 0x0002, "controlSOT" },
                                                                { 0x0001, "controlSTX" },
                                                                { 0x001a, "controlSUB" },
                                                                { 0x0016, "controlSYN" },
                                                                { 0x001f, "controlUS" },
                                                                { 0x000b, "controlVT" },
                                                                { 0x00a9, "copyright" },
                                                                { 0xf8e9, "copyrightsans" },
                                                                { 0xf6d9, "copyrightserif" },
                                                                { 0x300c, "cornerbracketleft" },
                                                                { 0xff62, "cornerbracketlefthalfwidth" },
                                                                { 0xfe41, "cornerbracketleftvertical" },
                                                                { 0x300d, "cornerbracketright" },
                                                                { 0xff63, "cornerbracketrighthalfwidth" },
                                                                { 0xfe42, "cornerbracketrightvertical" },
                                                                { 0x337f, "corporationsquare" },
                                                                { 0x33c7, "cosquare" },
                                                                { 0x33c6, "coverkgsquare" },
                                                                { 0x249e, "cparen" },
                                                                { 0x20a2, "cruzeiro" },
                                                                { 0x0297, "cstretched" },
                                                                { 0x22cf, "curlyand" },
                                                                { 0x22ce, "curlyor" },
                                                                { 0x00a4, "currency" },
                                                                { 0xf6d1, "cyrBreve" },
                                                                { 0xf6d2, "cyrFlex" },
                                                                { 0xf6d4, "cyrbreve" },
                                                                { 0xf6d5, "cyrflex" },
                                                                { 0x0064, "d" },
                                                                { 0x0564, "daarmenian" },
                                                                { 0x09a6, "dabengali" },
                                                                { 0x0636, "dadarabic" },
                                                                { 0x0926, "dadeva" },
                                                                { 0xfebe, "dadfinalarabic" },
                                                                { 0xfebf, "dadinitialarabic" },
                                                                { 0xfec0, "dadmedialarabic" },
                                                                { 0x05bc, "dagesh" },
                                                                { 0x05bc, "dageshhebrew" },
                                                                { 0x2020, "dagger" },
                                                                { 0x2021, "daggerdbl" },
                                                                { 0x0aa6, "dagujarati" },
                                                                { 0x0a26, "dagurmukhi" },
                                                                { 0x3060, "dahiragana" },
                                                                { 0x30c0, "dakatakana" },
                                                                { 0x062f, "dalarabic" },
                                                                { 0x05d3, "dalet" },
                                                                { 0xfb33, "daletdagesh" },
                                                                { 0xfb33, "daletdageshhebrew" },
                                                                { 0x05d3, "dalethebrew" },
                                                                { 0xfeaa, "dalfinalarabic" },
                                                                { 0x064f, "dammaarabic" },
                                                                { 0x064f, "dammalowarabic" },
                                                                { 0x064c, "dammatanaltonearabic" },
                                                                { 0x064c, "dammatanarabic" },
                                                                { 0x0964, "danda" },
                                                                { 0x05a7, "dargahebrew" },
                                                                { 0x05a7, "dargalefthebrew" },
                                                                { 0x0485, "dasiapneumatacyrilliccmb" },
                                                                { 0xf6d3, "dblGrave" },
                                                                { 0x300a, "dblanglebracketleft" },
                                                                { 0xfe3d, "dblanglebracketleftvertical" },
                                                                { 0x300b, "dblanglebracketright" },
                                                                { 0xfe3e, "dblanglebracketrightvertical" },
                                                                { 0x032b, "dblarchinvertedbelowcmb" },
                                                                { 0x21d4, "dblarrowleft" },
                                                                { 0x21d2, "dblarrowright" },
                                                                { 0x0965, "dbldanda" },
                                                                { 0xf6d6, "dblgrave" },
                                                                { 0x030f, "dblgravecmb" },
                                                                { 0x222c, "dblintegral" },
                                                                { 0x2017, "dbllowline" },
                                                                { 0x0333, "dbllowlinecmb" },
                                                                { 0x033f, "dbloverlinecmb" },
                                                                { 0x02ba, "dblprimemod" },
                                                                { 0x2016, "dblverticalbar" },
                                                                { 0x030e, "dblverticallineabovecmb" },
                                                                { 0x3109, "dbopomofo" },
                                                                { 0x33c8, "dbsquare" },
                                                                { 0x010f, "dcaron" },
                                                                { 0x1e11, "dcedilla" },
                                                                { 0x24d3, "dcircle" },
                                                                { 0x1e13, "dcircumflexbelow" },
                                                                { 0x0111, "dcroat" },
                                                                { 0x09a1, "ddabengali" },
                                                                { 0x0921, "ddadeva" },
                                                                { 0x0aa1, "ddagujarati" },
                                                                { 0x0a21, "ddagurmukhi" },
                                                                { 0x0688, "ddalarabic" },
                                                                { 0xfb89, "ddalfinalarabic" },
                                                                { 0x095c, "dddhadeva" },
                                                                { 0x09a2, "ddhabengali" },
                                                                { 0x0922, "ddhadeva" },
                                                                { 0x0aa2, "ddhagujarati" },
                                                                { 0x0a22, "ddhagurmukhi" },
                                                                { 0x1e0b, "ddotaccent" },
                                                                { 0x1e0d, "ddotbelow" },
                                                                { 0x066b, "decimalseparatorarabic" },
                                                                { 0x066b, "decimalseparatorpersian" },
                                                                { 0x0434, "decyrillic" },
                                                                { 0x00b0, "degree" },
                                                                { 0x05ad, "dehihebrew" },
                                                                { 0x3067, "dehiragana" },
                                                                { 0x03ef, "deicoptic" },
                                                                { 0x30c7, "dekatakana" },
                                                                { 0x232b, "deleteleft" },
                                                                { 0x2326, "deleteright" },
                                                                { 0x03b4, "delta" },
                                                                { 0x018d, "deltaturned" },
                                                                { 0x09f8, "denominatorminusonenumeratorbengali" },
                                                                { 0x02a4, "dezh" },
                                                                { 0x09a7, "dhabengali" },
                                                                { 0x0927, "dhadeva" },
                                                                { 0x0aa7, "dhagujarati" },
                                                                { 0x0a27, "dhagurmukhi" },
                                                                { 0x0257, "dhook" },
                                                                { 0x0385, "dialytikatonos" },
                                                                { 0x0344, "dialytikatonoscmb" },
                                                                { 0x2666, "diamond" },
                                                                { 0x2662, "diamondsuitwhite" },
                                                                { 0x00a8, "dieresis" },
                                                                { 0xf6d7, "dieresisacute" },
                                                                { 0x0324, "dieresisbelowcmb" },
                                                                { 0x0308, "dieresiscmb" },
                                                                { 0xf6d8, "dieresisgrave" },
                                                                { 0x0385, "dieresistonos" },
                                                                { 0x3062, "dihiragana" },
                                                                { 0x30c2, "dikatakana" },
                                                                { 0x3003, "dittomark" },
                                                                { 0x00f7, "divide" },
                                                                { 0x2223, "divides" },
                                                                { 0x2215, "divisionslash" },
                                                                { 0x0452, "djecyrillic" },
                                                                { 0x2593, "dkshade" },
                                                                { 0x1e0f, "dlinebelow" },
                                                                { 0x3397, "dlsquare" },
                                                                { 0x0111, "dmacron" },
                                                                { 0xff44, "dmonospace" },
                                                                { 0x2584, "dnblock" },
                                                                { 0x0e0e, "dochadathai" },
                                                                { 0x0e14, "dodekthai" },
                                                                { 0x3069, "dohiragana" },
                                                                { 0x30c9, "dokatakana" },
                                                                { 0x0024, "dollar" },
                                                                { 0xf6e3, "dollarinferior" },
                                                                { 0xff04, "dollarmonospace" },
                                                                { 0xf724, "dollaroldstyle" },
                                                                { 0xfe69, "dollarsmall" },
                                                                { 0xf6e4, "dollarsuperior" },
                                                                { 0x20ab, "dong" },
                                                                { 0x3326, "dorusquare" },
                                                                { 0x02d9, "dotaccent" },
                                                                { 0x0307, "dotaccentcmb" },
                                                                { 0x0323, "dotbelowcmb" },
                                                                { 0x0323, "dotbelowcomb" },
                                                                { 0x30fb, "dotkatakana" },
                                                                { 0x0131, "dotlessi" },
                                                                { 0xf6be, "dotlessj" },
                                                                { 0x0284, "dotlessjstrokehook" },
                                                                { 0x22c5, "dotmath" },
                                                                { 0x25cc, "dottedcircle" },
                                                                { 0xfb1f, "doubleyodpatah" },
                                                                { 0xfb1f, "doubleyodpatahhebrew" },
                                                                { 0x031e, "downtackbelowcmb" },
                                                                { 0x02d5, "downtackmod" },
                                                                { 0x249f, "dparen" },
                                                                { 0xf6eb, "dsuperior" },
                                                                { 0x0256, "dtail" },
                                                                { 0x018c, "dtopbar" },
                                                                { 0x3065, "duhiragana" },
                                                                { 0x30c5, "dukatakana" },
                                                                { 0x01f3, "dz" },
                                                                { 0x02a3, "dzaltone" },
                                                                { 0x01c6, "dzcaron" },
                                                                { 0x02a5, "dzcurl" },
                                                                { 0x04e1, "dzeabkhasiancyrillic" },
                                                                { 0x0455, "dzecyrillic" },
                                                                { 0x045f, "dzhecyrillic" },
                                                                { 0x0065, "e" },
                                                                { 0x00e9, "eacute" },
                                                                { 0x2641, "earth" },
                                                                { 0x098f, "ebengali" },
                                                                { 0x311c, "ebopomofo" },
                                                                { 0x0115, "ebreve" },
                                                                { 0x090d, "ecandradeva" },
                                                                { 0x0a8d, "ecandragujarati" },
                                                                { 0x0945, "ecandravowelsigndeva" },
                                                                { 0x0ac5, "ecandravowelsigngujarati" },
                                                                { 0x011b, "ecaron" },
                                                                { 0x1e1d, "ecedillabreve" },
                                                                { 0x0565, "echarmenian" },
                                                                { 0x0587, "echyiwnarmenian" },
                                                                { 0x24d4, "ecircle" },
                                                                { 0x00ea, "ecircumflex" },
                                                                { 0x1ebf, "ecircumflexacute" },
                                                                { 0x1e19, "ecircumflexbelow" },
                                                                { 0x1ec7, "ecircumflexdotbelow" },
                                                                { 0x1ec1, "ecircumflexgrave" },
                                                                { 0x1ec3, "ecircumflexhookabove" },
                                                                { 0x1ec5, "ecircumflextilde" },
                                                                { 0x0454, "ecyrillic" },
                                                                { 0x0205, "edblgrave" },
                                                                { 0x090f, "edeva" },
                                                                { 0x00eb, "edieresis" },
                                                                { 0x0117, "edot" },
                                                                { 0x0117, "edotaccent" },
                                                                { 0x1eb9, "edotbelow" },
                                                                { 0x0a0f, "eegurmukhi" },
                                                                { 0x0a47, "eematragurmukhi" },
                                                                { 0x0444, "efcyrillic" },
                                                                { 0x00e8, "egrave" },
                                                                { 0x0a8f, "egujarati" },
                                                                { 0x0567, "eharmenian" },
                                                                { 0x311d, "ehbopomofo" },
                                                                { 0x3048, "ehiragana" },
                                                                { 0x1ebb, "ehookabove" },
                                                                { 0x311f, "eibopomofo" },
                                                                { 0x0038, "eight" },
                                                                { 0x0668, "eightarabic" },
                                                                { 0x09ee, "eightbengali" },
                                                                { 0x2467, "eightcircle" },
                                                                { 0x2791, "eightcircleinversesansserif" },
                                                                { 0x096e, "eightdeva" },
                                                                { 0x2471, "eighteencircle" },
                                                                { 0x2485, "eighteenparen" },
                                                                { 0x2499, "eighteenperiod" },
                                                                { 0x0aee, "eightgujarati" },
                                                                { 0x0a6e, "eightgurmukhi" },
                                                                { 0x0668, "eighthackarabic" },
                                                                { 0x3028, "eighthangzhou" },
                                                                { 0x266b, "eighthnotebeamed" },
                                                                { 0x3227, "eightideographicparen" },
                                                                { 0x2088, "eightinferior" },
                                                                { 0xff18, "eightmonospace" },
                                                                { 0xf738, "eightoldstyle" },
                                                                { 0x247b, "eightparen" },
                                                                { 0x248f, "eightperiod" },
                                                                { 0x06f8, "eightpersian" },
                                                                { 0x2177, "eightroman" },
                                                                { 0x2078, "eightsuperior" },
                                                                { 0x0e58, "eightthai" },
                                                                { 0x0207, "einvertedbreve" },
                                                                { 0x0465, "eiotifiedcyrillic" },
                                                                { 0x30a8, "ekatakana" },
                                                                { 0xff74, "ekatakanahalfwidth" },
                                                                { 0x0a74, "ekonkargurmukhi" },
                                                                { 0x3154, "ekorean" },
                                                                { 0x043b, "elcyrillic" },
                                                                { 0x2208, "element" },
                                                                { 0x246a, "elevencircle" },
                                                                { 0x247e, "elevenparen" },
                                                                { 0x2492, "elevenperiod" },
                                                                { 0x217a, "elevenroman" },
                                                                { 0x2026, "ellipsis" },
                                                                { 0x22ee, "ellipsisvertical" },
                                                                { 0x0113, "emacron" },
                                                                { 0x1e17, "emacronacute" },
                                                                { 0x1e15, "emacrongrave" },
                                                                { 0x043c, "emcyrillic" },
                                                                { 0x2014, "emdash" },
                                                                { 0xfe31, "emdashvertical" },
                                                                { 0xff45, "emonospace" },
                                                                { 0x055b, "emphasismarkarmenian" },
                                                                { 0x2205, "emptyset" },
                                                                { 0x3123, "enbopomofo" },
                                                                { 0x043d, "encyrillic" },
                                                                { 0x2013, "endash" },
                                                                { 0xfe32, "endashvertical" },
                                                                { 0x04a3, "endescendercyrillic" },
                                                                { 0x014b, "eng" },
                                                                { 0x3125, "engbopomofo" },
                                                                { 0x04a5, "enghecyrillic" },
                                                                { 0x04c8, "enhookcyrillic" },
                                                                { 0x2002, "enspace" },
                                                                { 0x0119, "eogonek" },
                                                                { 0x3153, "eokorean" },
                                                                { 0x025b, "eopen" },
                                                                { 0x029a, "eopenclosed" },
                                                                { 0x025c, "eopenreversed" },
                                                                { 0x025e, "eopenreversedclosed" },
                                                                { 0x025d, "eopenreversedhook" },
                                                                { 0x24a0, "eparen" },
                                                                { 0x03b5, "epsilon" },
                                                                { 0x03ad, "epsilontonos" },
                                                                { 0x003d, "equal" },
                                                                { 0xff1d, "equalmonospace" },
                                                                { 0xfe66, "equalsmall" },
                                                                { 0x207c, "equalsuperior" },
                                                                { 0x2261, "equivalence" },
                                                                { 0x3126, "erbopomofo" },
                                                                { 0x0440, "ercyrillic" },
                                                                { 0x0258, "ereversed" },
                                                                { 0x044d, "ereversedcyrillic" },
                                                                { 0x0441, "escyrillic" },
                                                                { 0x04ab, "esdescendercyrillic" },
                                                                { 0x0283, "esh" },
                                                                { 0x0286, "eshcurl" },
                                                                { 0x090e, "eshortdeva" },
                                                                { 0x0946, "eshortvowelsigndeva" },
                                                                { 0x01aa, "eshreversedloop" },
                                                                { 0x0285, "eshsquatreversed" },
                                                                { 0x3047, "esmallhiragana" },
                                                                { 0x30a7, "esmallkatakana" },
                                                                { 0xff6a, "esmallkatakanahalfwidth" },
                                                                { 0x212e, "estimated" },
                                                                { 0xf6ec, "esuperior" },
                                                                { 0x03b7, "eta" },
                                                                { 0x0568, "etarmenian" },
                                                                { 0x03ae, "etatonos" },
                                                                { 0x00f0, "eth" },
                                                                { 0x1ebd, "etilde" },
                                                                { 0x1e1b, "etildebelow" },
                                                                { 0x0591, "etnahtafoukhhebrew" },
                                                                { 0x0591, "etnahtafoukhlefthebrew" },
                                                                { 0x0591, "etnahtahebrew" },
                                                                { 0x0591, "etnahtalefthebrew" },
                                                                { 0x01dd, "eturned" },
                                                                { 0x3161, "eukorean" },
                                                                { 0x20ac, "euro" },
                                                                { 0x09c7, "evowelsignbengali" },
                                                                { 0x0947, "evowelsigndeva" },
                                                                { 0x0ac7, "evowelsigngujarati" },
                                                                { 0x0021, "exclam" },
                                                                { 0x055c, "exclamarmenian" },
                                                                { 0x203c, "exclamdbl" },
                                                                { 0x00a1, "exclamdown" },
                                                                { 0xf7a1, "exclamdownsmall" },
                                                                { 0x0021, "exclamleft" },
                                                                { 0xff01, "exclammonospace" },
                                                                { 0xf721, "exclamsmall" },
                                                                { 0x2203, "existential" },
                                                                { 0x0292, "ezh" },
                                                                { 0x01ef, "ezhcaron" },
                                                                { 0x0293, "ezhcurl" },
                                                                { 0x01b9, "ezhreversed" },
                                                                { 0x01ba, "ezhtail" },
                                                                { 0x0066, "f" },
                                                                { 0x095e, "fadeva" },
                                                                { 0x0a5e, "fagurmukhi" },
                                                                { 0x2109, "fahrenheit" },
                                                                { 0x064e, "fathaarabic" },
                                                                { 0x064e, "fathalowarabic" },
                                                                { 0x064b, "fathatanarabic" },
                                                                { 0x3108, "fbopomofo" },
                                                                { 0x24d5, "fcircle" },
                                                                { 0x1e1f, "fdotaccent" },
                                                                { 0x0641, "feharabic" },
                                                                { 0x0586, "feharmenian" },
                                                                { 0xfed2, "fehfinalarabic" },
                                                                { 0xfed3, "fehinitialarabic" },
                                                                { 0xfed4, "fehmedialarabic" },
                                                                { 0x03e5, "feicoptic" },
                                                                { 0x2640, "female" },
                                                                { 0xfb00, "ff" },
                                                                { 0xfb03, "ffi" },
                                                                { 0xfb04, "ffl" },
                                                                { 0xfb01, "fi" },
                                                                { 0x246e, "fifteencircle" },
                                                                { 0x2482, "fifteenparen" },
                                                                { 0x2496, "fifteenperiod" },
                                                                { 0x2012, "figuredash" },
                                                                { 0x25a0, "filledbox" },
                                                                { 0x25ac, "filledrect" },
                                                                { 0x05da, "finalkaf" },
                                                                { 0xfb3a, "finalkafdagesh" },
                                                                { 0xfb3a, "finalkafdageshhebrew" },
                                                                { 0x05da, "finalkafhebrew" },
                                                                { 0x05dd, "finalmem" },
                                                                { 0x05dd, "finalmemhebrew" },
                                                                { 0x05df, "finalnun" },
                                                                { 0x05df, "finalnunhebrew" },
                                                                { 0x05e3, "finalpe" },
                                                                { 0x05e3, "finalpehebrew" },
                                                                { 0x05e5, "finaltsadi" },
                                                                { 0x05e5, "finaltsadihebrew" },
                                                                { 0x02c9, "firsttonechinese" },
                                                                { 0x25c9, "fisheye" },
                                                                { 0x0473, "fitacyrillic" },
                                                                { 0x0035, "five" },
                                                                { 0x0665, "fivearabic" },
                                                                { 0x09eb, "fivebengali" },
                                                                { 0x2464, "fivecircle" },
                                                                { 0x278e, "fivecircleinversesansserif" },
                                                                { 0x096b, "fivedeva" },
                                                                { 0x215d, "fiveeighths" },
                                                                { 0x0aeb, "fivegujarati" },
                                                                { 0x0a6b, "fivegurmukhi" },
                                                                { 0x0665, "fivehackarabic" },
                                                                { 0x3025, "fivehangzhou" },
                                                                { 0x3224, "fiveideographicparen" },
                                                                { 0x2085, "fiveinferior" },
                                                                { 0xff15, "fivemonospace" },
                                                                { 0xf735, "fiveoldstyle" },
                                                                { 0x2478, "fiveparen" },
                                                                { 0x248c, "fiveperiod" },
                                                                { 0x06f5, "fivepersian" },
                                                                { 0x2174, "fiveroman" },
                                                                { 0x2075, "fivesuperior" },
                                                                { 0x0e55, "fivethai" },
                                                                { 0xfb02, "fl" },
                                                                { 0x0192, "florin" },
                                                                { 0xff46, "fmonospace" },
                                                                { 0x3399, "fmsquare" },
                                                                { 0x0e1f, "fofanthai" },
                                                                { 0x0e1d, "fofathai" },
                                                                { 0x0e4f, "fongmanthai" },
                                                                { 0x2200, "forall" },
                                                                { 0x0034, "four" },
                                                                { 0x0664, "fourarabic" },
                                                                { 0x09ea, "fourbengali" },
                                                                { 0x2463, "fourcircle" },
                                                                { 0x278d, "fourcircleinversesansserif" },
                                                                { 0x096a, "fourdeva" },
                                                                { 0x0aea, "fourgujarati" },
                                                                { 0x0a6a, "fourgurmukhi" },
                                                                { 0x0664, "fourhackarabic" },
                                                                { 0x3024, "fourhangzhou" },
                                                                { 0x3223, "fourideographicparen" },
                                                                { 0x2084, "fourinferior" },
                                                                { 0xff14, "fourmonospace" },
                                                                { 0x09f7, "fournumeratorbengali" },
                                                                { 0xf734, "fouroldstyle" },
                                                                { 0x2477, "fourparen" },
                                                                { 0x248b, "fourperiod" },
                                                                { 0x06f4, "fourpersian" },
                                                                { 0x2173, "fourroman" },
                                                                { 0x2074, "foursuperior" },
                                                                { 0x246d, "fourteencircle" },
                                                                { 0x2481, "fourteenparen" },
                                                                { 0x2495, "fourteenperiod" },
                                                                { 0x0e54, "fourthai" },
                                                                { 0x02cb, "fourthtonechinese" },
                                                                { 0x24a1, "fparen" },
                                                                { 0x2044, "fraction" },
                                                                { 0x20a3, "franc" },
                                                                { 0x0067, "g" },
                                                                { 0x0997, "gabengali" },
                                                                { 0x01f5, "gacute" },
                                                                { 0x0917, "gadeva" },
                                                                { 0x06af, "gafarabic" },
                                                                { 0xfb93, "gaffinalarabic" },
                                                                { 0xfb94, "gafinitialarabic" },
                                                                { 0xfb95, "gafmedialarabic" },
                                                                { 0x0a97, "gagujarati" },
                                                                { 0x0a17, "gagurmukhi" },
                                                                { 0x304c, "gahiragana" },
                                                                { 0x30ac, "gakatakana" },
                                                                { 0x03b3, "gamma" },
                                                                { 0x0263, "gammalatinsmall" },
                                                                { 0x02e0, "gammasuperior" },
                                                                { 0x03eb, "gangiacoptic" },
                                                                { 0x310d, "gbopomofo" },
                                                                { 0x011f, "gbreve" },
                                                                { 0x01e7, "gcaron" },
                                                                { 0x0123, "gcedilla" },
                                                                { 0x24d6, "gcircle" },
                                                                { 0x011d, "gcircumflex" },
                                                                { 0x0123, "gcommaaccent" },
                                                                { 0x0121, "gdot" },
                                                                { 0x0121, "gdotaccent" },
                                                                { 0x0433, "gecyrillic" },
                                                                { 0x3052, "gehiragana" },
                                                                { 0x30b2, "gekatakana" },
                                                                { 0x2251, "geometricallyequal" },
                                                                { 0x059c, "gereshaccenthebrew" },
                                                                { 0x05f3, "gereshhebrew" },
                                                                { 0x059d, "gereshmuqdamhebrew" },
                                                                { 0x00df, "germandbls" },
                                                                { 0x059e, "gershayimaccenthebrew" },
                                                                { 0x05f4, "gershayimhebrew" },
                                                                { 0x3013, "getamark" },
                                                                { 0x0998, "ghabengali" },
                                                                { 0x0572, "ghadarmenian" },
                                                                { 0x0918, "ghadeva" },
                                                                { 0x0a98, "ghagujarati" },
                                                                { 0x0a18, "ghagurmukhi" },
                                                                { 0x063a, "ghainarabic" },
                                                                { 0xfece, "ghainfinalarabic" },
                                                                { 0xfecf, "ghaininitialarabic" },
                                                                { 0xfed0, "ghainmedialarabic" },
                                                                { 0x0495, "ghemiddlehookcyrillic" },
                                                                { 0x0493, "ghestrokecyrillic" },
                                                                { 0x0491, "gheupturncyrillic" },
                                                                { 0x095a, "ghhadeva" },
                                                                { 0x0a5a, "ghhagurmukhi" },
                                                                { 0x0260, "ghook" },
                                                                { 0x3393, "ghzsquare" },
                                                                { 0x304e, "gihiragana" },
                                                                { 0x30ae, "gikatakana" },
                                                                { 0x0563, "gimarmenian" },
                                                                { 0x05d2, "gimel" },
                                                                { 0xfb32, "gimeldagesh" },
                                                                { 0xfb32, "gimeldageshhebrew" },
                                                                { 0x05d2, "gimelhebrew" },
                                                                { 0x0453, "gjecyrillic" },
                                                                { 0x01be, "glottalinvertedstroke" },
                                                                { 0x0294, "glottalstop" },
                                                                { 0x0296, "glottalstopinverted" },
                                                                { 0x02c0, "glottalstopmod" },
                                                                { 0x0295, "glottalstopreversed" },
                                                                { 0x02c1, "glottalstopreversedmod" },
                                                                { 0x02e4, "glottalstopreversedsuperior" },
                                                                { 0x02a1, "glottalstopstroke" },
                                                                { 0x02a2, "glottalstopstrokereversed" },
                                                                { 0x1e21, "gmacron" },
                                                                { 0xff47, "gmonospace" },
                                                                { 0x3054, "gohiragana" },
                                                                { 0x30b4, "gokatakana" },
                                                                { 0x24a2, "gparen" },
                                                                { 0x33ac, "gpasquare" },
                                                                { 0x2207, "gradient" },
                                                                { 0x0060, "grave" },
                                                                { 0x0316, "gravebelowcmb" },
                                                                { 0x0300, "gravecmb" },
                                                                { 0x0300, "gravecomb" },
                                                                { 0x0953, "gravedeva" },
                                                                { 0x02ce, "gravelowmod" },
                                                                { 0xff40, "gravemonospace" },
                                                                { 0x0340, "gravetonecmb" },
                                                                { 0x003e, "greater" },
                                                                { 0x2265, "greaterequal" },
                                                                { 0x22db, "greaterequalorless" },
                                                                { 0xff1e, "greatermonospace" },
                                                                { 0x2a7e, "greaterorequalslant" },
                                                                { 0x2273, "greaterorequivalent" },
                                                                { 0x2277, "greaterorless" },
                                                                { 0x2267, "greateroverequal" },
                                                                { 0xfe65, "greatersmall" },
                                                                { 0x0261, "gscript" },
                                                                { 0x01e5, "gstroke" },
                                                                { 0x3050, "guhiragana" },
                                                                { 0x00ab, "guillemotleft" },
                                                                { 0x00bb, "guillemotright" },
                                                                { 0x2039, "guilsinglleft" },
                                                                { 0x203a, "guilsinglright" },
                                                                { 0x30b0, "gukatakana" },
                                                                { 0x3318, "guramusquare" },
                                                                { 0x33c9, "gysquare" },
                                                                { 0x0068, "h" },
                                                                { 0x04a9, "haabkhasiancyrillic" },
                                                                { 0x06c1, "haaltonearabic" },
                                                                { 0x09b9, "habengali" },
                                                                { 0x04b3, "hadescendercyrillic" },
                                                                { 0x0939, "hadeva" },
                                                                { 0x0ab9, "hagujarati" },
                                                                { 0x0a39, "hagurmukhi" },
                                                                { 0x062d, "haharabic" },
                                                                { 0xfea2, "hahfinalarabic" },
                                                                { 0xfea3, "hahinitialarabic" },
                                                                { 0x306f, "hahiragana" },
                                                                { 0xfea4, "hahmedialarabic" },
                                                                { 0x332a, "haitusquare" },
                                                                { 0x30cf, "hakatakana" },
                                                                { 0xff8a, "hakatakanahalfwidth" },
                                                                { 0x0a4d, "halantgurmukhi" },
                                                                { 0x0621, "hamzaarabic" },
                                                                { 0x0621, "hamzalowarabic" },
                                                                { 0x3164, "hangulfiller" },
                                                                { 0x044a, "hardsigncyrillic" },
                                                                { 0x21bc, "harpoonleftbarbup" },
                                                                { 0x21c0, "harpoonrightbarbup" },
                                                                { 0x33ca, "hasquare" },
                                                                { 0x05b2, "hatafpatah" },
                                                                { 0x05b2, "hatafpatah16" },
                                                                { 0x05b2, "hatafpatah23" },
                                                                { 0x05b2, "hatafpatah2f" },
                                                                { 0x05b2, "hatafpatahhebrew" },
                                                                { 0x05b2, "hatafpatahnarrowhebrew" },
                                                                { 0x05b2, "hatafpatahquarterhebrew" },
                                                                { 0x05b2, "hatafpatahwidehebrew" },
                                                                { 0x05b3, "hatafqamats" },
                                                                { 0x05b3, "hatafqamats1b" },
                                                                { 0x05b3, "hatafqamats28" },
                                                                { 0x05b3, "hatafqamats34" },
                                                                { 0x05b3, "hatafqamatshebrew" },
                                                                { 0x05b3, "hatafqamatsnarrowhebrew" },
                                                                { 0x05b3, "hatafqamatsquarterhebrew" },
                                                                { 0x05b3, "hatafqamatswidehebrew" },
                                                                { 0x05b1, "hatafsegol" },
                                                                { 0x05b1, "hatafsegol17" },
                                                                { 0x05b1, "hatafsegol24" },
                                                                { 0x05b1, "hatafsegol30" },
                                                                { 0x05b1, "hatafsegolhebrew" },
                                                                { 0x05b1, "hatafsegolnarrowhebrew" },
                                                                { 0x05b1, "hatafsegolquarterhebrew" },
                                                                { 0x05b1, "hatafsegolwidehebrew" },
                                                                { 0x0127, "hbar" },
                                                                { 0x310f, "hbopomofo" },
                                                                { 0x1e2b, "hbrevebelow" },
                                                                { 0x1e29, "hcedilla" },
                                                                { 0x24d7, "hcircle" },
                                                                { 0x0125, "hcircumflex" },
                                                                { 0x1e27, "hdieresis" },
                                                                { 0x1e23, "hdotaccent" },
                                                                { 0x1e25, "hdotbelow" },
                                                                { 0x05d4, "he" },
                                                                { 0x2665, "heart" },
                                                                { 0x2665, "heartsuitblack" },
                                                                { 0x2661, "heartsuitwhite" },
                                                                { 0xfb34, "hedagesh" },
                                                                { 0xfb34, "hedageshhebrew" },
                                                                { 0x06c1, "hehaltonearabic" },
                                                                { 0x0647, "heharabic" },
                                                                { 0x05d4, "hehebrew" },
                                                                { 0xfba7, "hehfinalaltonearabic" },
                                                                { 0xfeea, "hehfinalalttwoarabic" },
                                                                { 0xfeea, "hehfinalarabic" },
                                                                { 0xfba5, "hehhamzaabovefinalarabic" },
                                                                { 0xfba4, "hehhamzaaboveisolatedarabic" },
                                                                { 0xfba8, "hehinitialaltonearabic" },
                                                                { 0xfeeb, "hehinitialarabic" },
                                                                { 0x3078, "hehiragana" },
                                                                { 0xfba9, "hehmedialaltonearabic" },
                                                                { 0xfeec, "hehmedialarabic" },
                                                                { 0x337b, "heiseierasquare" },
                                                                { 0x30d8, "hekatakana" },
                                                                { 0xff8d, "hekatakanahalfwidth" },
                                                                { 0x3336, "hekutaarusquare" },
                                                                { 0x0267, "henghook" },
                                                                { 0x3339, "herutusquare" },
                                                                { 0x05d7, "het" },
                                                                { 0x05d7, "hethebrew" },
                                                                { 0x0266, "hhook" },
                                                                { 0x02b1, "hhooksuperior" },
                                                                { 0x327b, "hieuhacirclekorean" },
                                                                { 0x321b, "hieuhaparenkorean" },
                                                                { 0x326d, "hieuhcirclekorean" },
                                                                { 0x314e, "hieuhkorean" },
                                                                { 0x320d, "hieuhparenkorean" },
                                                                { 0x3072, "hihiragana" },
                                                                { 0x30d2, "hikatakana" },
                                                                { 0xff8b, "hikatakanahalfwidth" },
                                                                { 0x05b4, "hiriq" },
                                                                { 0x05b4, "hiriq14" },
                                                                { 0x05b4, "hiriq21" },
                                                                { 0x05b4, "hiriq2d" },
                                                                { 0x05b4, "hiriqhebrew" },
                                                                { 0x05b4, "hiriqnarrowhebrew" },
                                                                { 0x05b4, "hiriqquarterhebrew" },
                                                                { 0x05b4, "hiriqwidehebrew" },
                                                                { 0x1e96, "hlinebelow" },
                                                                { 0xff48, "hmonospace" },
                                                                { 0x0570, "hoarmenian" },
                                                                { 0x0e2b, "hohipthai" },
                                                                { 0x307b, "hohiragana" },
                                                                { 0x30db, "hokatakana" },
                                                                { 0xff8e, "hokatakanahalfwidth" },
                                                                { 0x05b9, "holam" },
                                                                { 0x05b9, "holam19" },
                                                                { 0x05b9, "holam26" },
                                                                { 0x05b9, "holam32" },
                                                                { 0x05b9, "holamhebrew" },
                                                                { 0x05b9, "holamnarrowhebrew" },
                                                                { 0x05b9, "holamquarterhebrew" },
                                                                { 0x05b9, "holamwidehebrew" },
                                                                { 0x0e2e, "honokhukthai" },
                                                                { 0x0309, "hookabovecomb" },
                                                                { 0x0309, "hookcmb" },
                                                                { 0x0321, "hookpalatalizedbelowcmb" },
                                                                { 0x0322, "hookretroflexbelowcmb" },
                                                                { 0x3342, "hoonsquare" },
                                                                { 0x03e9, "horicoptic" },
                                                                { 0x2015, "horizontalbar" },
                                                                { 0x031b, "horncmb" },
                                                                { 0x2668, "hotsprings" },
                                                                { 0x2302, "house" },
                                                                { 0x24a3, "hparen" },
                                                                { 0x02b0, "hsuperior" },
                                                                { 0x0265, "hturned" },
                                                                { 0x3075, "huhiragana" },
                                                                { 0x3333, "huiitosquare" },
                                                                { 0x30d5, "hukatakana" },
                                                                { 0xff8c, "hukatakanahalfwidth" },
                                                                { 0x02dd, "hungarumlaut" },
                                                                { 0x030b, "hungarumlautcmb" },
                                                                { 0x0195, "hv" },
                                                                { 0x002d, "hyphen" },
                                                                { 0xf6e5, "hypheninferior" },
                                                                { 0xff0d, "hyphenmonospace" },
                                                                { 0xfe63, "hyphensmall" },
                                                                { 0xf6e6, "hyphensuperior" },
                                                                { 0x2010, "hyphentwo" },
                                                                { 0x0069, "i" },
                                                                { 0x00ed, "iacute" },
                                                                { 0x044f, "iacyrillic" },
                                                                { 0x0987, "ibengali" },
                                                                { 0x3127, "ibopomofo" },
                                                                { 0x012d, "ibreve" },
                                                                { 0x01d0, "icaron" },
                                                                { 0x24d8, "icircle" },
                                                                { 0x00ee, "icircumflex" },
                                                                { 0x0456, "icyrillic" },
                                                                { 0x0209, "idblgrave" },
                                                                { 0x328f, "ideographearthcircle" },
                                                                { 0x328b, "ideographfirecircle" },
                                                                { 0x323f, "ideographicallianceparen" },
                                                                { 0x323a, "ideographiccallparen" },
                                                                { 0x32a5, "ideographiccentrecircle" },
                                                                { 0x3006, "ideographicclose" },
                                                                { 0x3001, "ideographiccomma" },
                                                                { 0xff64, "ideographiccommaleft" },
                                                                { 0x3237, "ideographiccongratulationparen" },
                                                                { 0x32a3, "ideographiccorrectcircle" },
                                                                { 0x322f, "ideographicearthparen" },
                                                                { 0x323d, "ideographicenterpriseparen" },
                                                                { 0x329d, "ideographicexcellentcircle" },
                                                                { 0x3240, "ideographicfestivalparen" },
                                                                { 0x3296, "ideographicfinancialcircle" },
                                                                { 0x3236, "ideographicfinancialparen" },
                                                                { 0x322b, "ideographicfireparen" },
                                                                { 0x3232, "ideographichaveparen" },
                                                                { 0x32a4, "ideographichighcircle" },
                                                                { 0x3005, "ideographiciterationmark" },
                                                                { 0x3298, "ideographiclaborcircle" },
                                                                { 0x3238, "ideographiclaborparen" },
                                                                { 0x32a7, "ideographicleftcircle" },
                                                                { 0x32a6, "ideographiclowcircle" },
                                                                { 0x32a9, "ideographicmedicinecircle" },
                                                                { 0x322e, "ideographicmetalparen" },
                                                                { 0x322a, "ideographicmoonparen" },
                                                                { 0x3234, "ideographicnameparen" },
                                                                { 0x3002, "ideographicperiod" },
                                                                { 0x329e, "ideographicprintcircle" },
                                                                { 0x3243, "ideographicreachparen" },
                                                                { 0x3239, "ideographicrepresentparen" },
                                                                { 0x323e, "ideographicresourceparen" },
                                                                { 0x32a8, "ideographicrightcircle" },
                                                                { 0x3299, "ideographicsecretcircle" },
                                                                { 0x3242, "ideographicselfparen" },
                                                                { 0x3233, "ideographicsocietyparen" },
                                                                { 0x3000, "ideographicspace" },
                                                                { 0x3235, "ideographicspecialparen" },
                                                                { 0x3231, "ideographicstockparen" },
                                                                { 0x323b, "ideographicstudyparen" },
                                                                { 0x3230, "ideographicsunparen" },
                                                                { 0x323c, "ideographicsuperviseparen" },
                                                                { 0x322c, "ideographicwaterparen" },
                                                                { 0x322d, "ideographicwoodparen" },
                                                                { 0x3007, "ideographiczero" },
                                                                { 0x328e, "ideographmetalcircle" },
                                                                { 0x328a, "ideographmooncircle" },
                                                                { 0x3294, "ideographnamecircle" },
                                                                { 0x3290, "ideographsuncircle" },
                                                                { 0x328c, "ideographwatercircle" },
                                                                { 0x328d, "ideographwoodcircle" },
                                                                { 0x0907, "ideva" },
                                                                { 0x00ef, "idieresis" },
                                                                { 0x1e2f, "idieresisacute" },
                                                                { 0x04e5, "idieresiscyrillic" },
                                                                { 0x1ecb, "idotbelow" },
                                                                { 0x04d7, "iebrevecyrillic" },
                                                                { 0x0435, "iecyrillic" },
                                                                { 0x3275, "ieungacirclekorean" },
                                                                { 0x3215, "ieungaparenkorean" },
                                                                { 0x3267, "ieungcirclekorean" },
                                                                { 0x3147, "ieungkorean" },
                                                                { 0x3207, "ieungparenkorean" },
                                                                { 0x00ec, "igrave" },
                                                                { 0x0a87, "igujarati" },
                                                                { 0x0a07, "igurmukhi" },
                                                                { 0x3044, "ihiragana" },
                                                                { 0x1ec9, "ihookabove" },
                                                                { 0x0988, "iibengali" },
                                                                { 0x0438, "iicyrillic" },
                                                                { 0x0908, "iideva" },
                                                                { 0x0a88, "iigujarati" },
                                                                { 0x0a08, "iigurmukhi" },
                                                                { 0x0a40, "iimatragurmukhi" },
                                                                { 0x020b, "iinvertedbreve" },
                                                                { 0x0439, "iishortcyrillic" },
                                                                { 0x09c0, "iivowelsignbengali" },
                                                                { 0x0940, "iivowelsigndeva" },
                                                                { 0x0ac0, "iivowelsigngujarati" },
                                                                { 0x0133, "ij" },
                                                                { 0x30a4, "ikatakana" },
                                                                { 0xff72, "ikatakanahalfwidth" },
                                                                { 0x3163, "ikorean" },
                                                                { 0x02dc, "ilde" },
                                                                { 0x05ac, "iluyhebrew" },
                                                                { 0x012b, "imacron" },
                                                                { 0x04e3, "imacroncyrillic" },
                                                                { 0x2253, "imageorapproximatelyequal" },
                                                                { 0x0a3f, "imatragurmukhi" },
                                                                { 0xff49, "imonospace" },
                                                                { 0x2206, "increment" },
                                                                { 0x221e, "infinity" },
                                                                { 0x056b, "iniarmenian" },
                                                                { 0x222b, "integral" },
                                                                { 0x2321, "integralbottom" },
                                                                { 0x2321, "integralbt" },
                                                                { 0xf8f5, "integralex" },
                                                                { 0x2320, "integraltop" },
                                                                { 0x2320, "integraltp" },
                                                                { 0x2229, "intersection" },
                                                                { 0x3305, "intisquare" },
                                                                { 0x25d8, "invbullet" },
                                                                { 0x25d9, "invcircle" },
                                                                { 0x263b, "invsmileface" },
                                                                { 0x0451, "iocyrillic" },
                                                                { 0x012f, "iogonek" },
                                                                { 0x03b9, "iota" },
                                                                { 0x03ca, "iotadieresis" },
                                                                { 0x0390, "iotadieresistonos" },
                                                                { 0x0269, "iotalatin" },
                                                                { 0x03af, "iotatonos" },
                                                                { 0x24a4, "iparen" },
                                                                { 0x0a72, "irigurmukhi" },
                                                                { 0x3043, "ismallhiragana" },
                                                                { 0x30a3, "ismallkatakana" },
                                                                { 0xff68, "ismallkatakanahalfwidth" },
                                                                { 0x09fa, "issharbengali" },
                                                                { 0x0268, "istroke" },
                                                                { 0xf6ed, "isuperior" },
                                                                { 0x309d, "iterationhiragana" },
                                                                { 0x30fd, "iterationkatakana" },
                                                                { 0x0129, "itilde" },
                                                                { 0x1e2d, "itildebelow" },
                                                                { 0x3129, "iubopomofo" },
                                                                { 0x044e, "iucyrillic" },
                                                                { 0x09bf, "ivowelsignbengali" },
                                                                { 0x093f, "ivowelsigndeva" },
                                                                { 0x0abf, "ivowelsigngujarati" },
                                                                { 0x0475, "izhitsacyrillic" },
                                                                { 0x0477, "izhitsadblgravecyrillic" },
                                                                { 0x006a, "j" },
                                                                { 0x0571, "jaarmenian" },
                                                                { 0x099c, "jabengali" },
                                                                { 0x091c, "jadeva" },
                                                                { 0x0a9c, "jagujarati" },
                                                                { 0x0a1c, "jagurmukhi" },
                                                                { 0x3110, "jbopomofo" },
                                                                { 0x01f0, "jcaron" },
                                                                { 0x24d9, "jcircle" },
                                                                { 0x0135, "jcircumflex" },
                                                                { 0x029d, "jcrossedtail" },
                                                                { 0x025f, "jdotlessstroke" },
                                                                { 0x0458, "jecyrillic" },
                                                                { 0x062c, "jeemarabic" },
                                                                { 0xfe9e, "jeemfinalarabic" },
                                                                { 0xfe9f, "jeeminitialarabic" },
                                                                { 0xfea0, "jeemmedialarabic" },
                                                                { 0x0698, "jeharabic" },
                                                                { 0xfb8b, "jehfinalarabic" },
                                                                { 0x099d, "jhabengali" },
                                                                { 0x091d, "jhadeva" },
                                                                { 0x0a9d, "jhagujarati" },
                                                                { 0x0a1d, "jhagurmukhi" },
                                                                { 0x057b, "jheharmenian" },
                                                                { 0x3004, "jis" },
                                                                { 0xff4a, "jmonospace" },
                                                                { 0x24a5, "jparen" },
                                                                { 0x02b2, "jsuperior" },
                                                                { 0x006b, "k" },
                                                                { 0x04a1, "kabashkircyrillic" },
                                                                { 0x0995, "kabengali" },
                                                                { 0x1e31, "kacute" },
                                                                { 0x043a, "kacyrillic" },
                                                                { 0x049b, "kadescendercyrillic" },
                                                                { 0x0915, "kadeva" },
                                                                { 0x05db, "kaf" },
                                                                { 0x0643, "kafarabic" },
                                                                { 0xfb3b, "kafdagesh" },
                                                                { 0xfb3b, "kafdageshhebrew" },
                                                                { 0xfeda, "kaffinalarabic" },
                                                                { 0x05db, "kafhebrew" },
                                                                { 0xfedb, "kafinitialarabic" },
                                                                { 0xfedc, "kafmedialarabic" },
                                                                { 0xfb4d, "kafrafehebrew" },
                                                                { 0x0a95, "kagujarati" },
                                                                { 0x0a15, "kagurmukhi" },
                                                                { 0x304b, "kahiragana" },
                                                                { 0x04c4, "kahookcyrillic" },
                                                                { 0x30ab, "kakatakana" },
                                                                { 0xff76, "kakatakanahalfwidth" },
                                                                { 0x03ba, "kappa" },
                                                                { 0x03f0, "kappasymbolgreek" },
                                                                { 0x3171, "kapyeounmieumkorean" },
                                                                { 0x3184, "kapyeounphieuphkorean" },
                                                                { 0x3178, "kapyeounpieupkorean" },
                                                                { 0x3179, "kapyeounssangpieupkorean" },
                                                                { 0x330d, "karoriisquare" },
                                                                { 0x0640, "kashidaautoarabic" },
                                                                { 0x0640, "kashidaautonosidebearingarabic" },
                                                                { 0x30f5, "kasmallkatakana" },
                                                                { 0x3384, "kasquare" },
                                                                { 0x0650, "kasraarabic" },
                                                                { 0x064d, "kasratanarabic" },
                                                                { 0x049f, "kastrokecyrillic" },
                                                                { 0xff70, "katahiraprolongmarkhalfwidth" },
                                                                { 0x049d, "kaverticalstrokecyrillic" },
                                                                { 0x310e, "kbopomofo" },
                                                                { 0x3389, "kcalsquare" },
                                                                { 0x01e9, "kcaron" },
                                                                { 0x0137, "kcedilla" },
                                                                { 0x24da, "kcircle" },
                                                                { 0x0137, "kcommaaccent" },
                                                                { 0x1e33, "kdotbelow" },
                                                                { 0x0584, "keharmenian" },
                                                                { 0x3051, "kehiragana" },
                                                                { 0x30b1, "kekatakana" },
                                                                { 0xff79, "kekatakanahalfwidth" },
                                                                { 0x056f, "kenarmenian" },
                                                                { 0x30f6, "kesmallkatakana" },
                                                                { 0x0138, "kgreenlandic" },
                                                                { 0x0996, "khabengali" },
                                                                { 0x0445, "khacyrillic" },
                                                                { 0x0916, "khadeva" },
                                                                { 0x0a96, "khagujarati" },
                                                                { 0x0a16, "khagurmukhi" },
                                                                { 0x062e, "khaharabic" },
                                                                { 0xfea6, "khahfinalarabic" },
                                                                { 0xfea7, "khahinitialarabic" },
                                                                { 0xfea8, "khahmedialarabic" },
                                                                { 0x03e7, "kheicoptic" },
                                                                { 0x0959, "khhadeva" },
                                                                { 0x0a59, "khhagurmukhi" },
                                                                { 0x3278, "khieukhacirclekorean" },
                                                                { 0x3218, "khieukhaparenkorean" },
                                                                { 0x326a, "khieukhcirclekorean" },
                                                                { 0x314b, "khieukhkorean" },
                                                                { 0x320a, "khieukhparenkorean" },
                                                                { 0x0e02, "khokhaithai" },
                                                                { 0x0e05, "khokhonthai" },
                                                                { 0x0e03, "khokhuatthai" },
                                                                { 0x0e04, "khokhwaithai" },
                                                                { 0x0e5b, "khomutthai" },
                                                                { 0x0199, "khook" },
                                                                { 0x0e06, "khorakhangthai" },
                                                                { 0x3391, "khzsquare" },
                                                                { 0x304d, "kihiragana" },
                                                                { 0x30ad, "kikatakana" },
                                                                { 0xff77, "kikatakanahalfwidth" },
                                                                { 0x3315, "kiroguramusquare" },
                                                                { 0x3316, "kiromeetorusquare" },
                                                                { 0x3314, "kirosquare" },
                                                                { 0x326e, "kiyeokacirclekorean" },
                                                                { 0x320e, "kiyeokaparenkorean" },
                                                                { 0x3260, "kiyeokcirclekorean" },
                                                                { 0x3131, "kiyeokkorean" },
                                                                { 0x3200, "kiyeokparenkorean" },
                                                                { 0x3133, "kiyeoksioskorean" },
                                                                { 0x045c, "kjecyrillic" },
                                                                { 0x1e35, "klinebelow" },
                                                                { 0x3398, "klsquare" },
                                                                { 0x33a6, "kmcubedsquare" },
                                                                { 0xff4b, "kmonospace" },
                                                                { 0x33a2, "kmsquaredsquare" },
                                                                { 0x3053, "kohiragana" },
                                                                { 0x33c0, "kohmsquare" },
                                                                { 0x0e01, "kokaithai" },
                                                                { 0x30b3, "kokatakana" },
                                                                { 0xff7a, "kokatakanahalfwidth" },
                                                                { 0x331e, "kooposquare" },
                                                                { 0x0481, "koppacyrillic" },
                                                                { 0x327f, "koreanstandardsymbol" },
                                                                { 0x0343, "koroniscmb" },
                                                                { 0x24a6, "kparen" },
                                                                { 0x33aa, "kpasquare" },
                                                                { 0x046f, "ksicyrillic" },
                                                                { 0x33cf, "ktsquare" },
                                                                { 0x029e, "kturned" },
                                                                { 0x304f, "kuhiragana" },
                                                                { 0x30af, "kukatakana" },
                                                                { 0xff78, "kukatakanahalfwidth" },
                                                                { 0x33b8, "kvsquare" },
                                                                { 0x33be, "kwsquare" },
                                                                { 0x006c, "l" },
                                                                { 0x09b2, "labengali" },
                                                                { 0x013a, "lacute" },
                                                                { 0x0932, "ladeva" },
                                                                { 0x0ab2, "lagujarati" },
                                                                { 0x0a32, "lagurmukhi" },
                                                                { 0x0e45, "lakkhangyaothai" },
                                                                { 0xfefc, "lamaleffinalarabic" },
                                                                { 0xfef8, "lamalefhamzaabovefinalarabic" },
                                                                { 0xfef7, "lamalefhamzaaboveisolatedarabic" },
                                                                { 0xfefa, "lamalefhamzabelowfinalarabic" },
                                                                { 0xfef9, "lamalefhamzabelowisolatedarabic" },
                                                                { 0xfefb, "lamalefisolatedarabic" },
                                                                { 0xfef6, "lamalefmaddaabovefinalarabic" },
                                                                { 0xfef5, "lamalefmaddaaboveisolatedarabic" },
                                                                { 0x0644, "lamarabic" },
                                                                { 0x03bb, "lambda" },
                                                                { 0x019b, "lambdastroke" },
                                                                { 0x05dc, "lamed" },
                                                                { 0xfb3c, "lameddagesh" },
                                                                { 0xfb3c, "lameddageshhebrew" },
                                                                { 0x05dc, "lamedhebrew" },
                                                                { 0xfede, "lamfinalarabic" },
                                                                { 0xfcca, "lamhahinitialarabic" },
                                                                { 0xfedf, "laminitialarabic" },
                                                                { 0xfcc9, "lamjeeminitialarabic" },
                                                                { 0xfccb, "lamkhahinitialarabic" },
                                                                { 0xfdf2, "lamlamhehisolatedarabic" },
                                                                { 0xfee0, "lammedialarabic" },
                                                                { 0xfd88, "lammeemhahinitialarabic" },
                                                                { 0xfccc, "lammeeminitialarabic" },
                                                                { 0x25ef, "largecircle" },
                                                                { 0x019a, "lbar" },
                                                                { 0x026c, "lbelt" },
                                                                { 0x310c, "lbopomofo" },
                                                                { 0x013e, "lcaron" },
                                                                { 0x013c, "lcedilla" },
                                                                { 0x24db, "lcircle" },
                                                                { 0x1e3d, "lcircumflexbelow" },
                                                                { 0x013c, "lcommaaccent" },
                                                                { 0x0140, "ldot" },
                                                                { 0x0140, "ldotaccent" },
                                                                { 0x1e37, "ldotbelow" },
                                                                { 0x1e39, "ldotbelowmacron" },
                                                                { 0x031a, "leftangleabovecmb" },
                                                                { 0x0318, "lefttackbelowcmb" },
                                                                { 0x003c, "less" },
                                                                { 0x2264, "lessequal" },
                                                                { 0x22da, "lessequalorgreater" },
                                                                { 0xff1c, "lessmonospace" },
                                                                { 0x2a7d, "lessorequalslant" },
                                                                { 0x2272, "lessorequivalent" },
                                                                { 0x2276, "lessorgreater" },
                                                                { 0x2266, "lessoverequal" },
                                                                { 0xfe64, "lesssmall" },
                                                                { 0x026e, "lezh" },
                                                                { 0x258c, "lfblock" },
                                                                { 0x026d, "lhookretroflex" },
                                                                { 0x20a4, "lira" },
                                                                { 0x056c, "liwnarmenian" },
                                                                { 0x01c9, "lj" },
                                                                { 0x0459, "ljecyrillic" },
                                                                { 0xf6c0, "ll" },
                                                                { 0x0933, "lladeva" },
                                                                { 0x0ab3, "llagujarati" },
                                                                { 0x1e3b, "llinebelow" },
                                                                { 0x0934, "llladeva" },
                                                                { 0x09e1, "llvocalicbengali" },
                                                                { 0x0961, "llvocalicdeva" },
                                                                { 0x09e3, "llvocalicvowelsignbengali" },
                                                                { 0x0963, "llvocalicvowelsigndeva" },
                                                                { 0x026b, "lmiddletilde" },
                                                                { 0xff4c, "lmonospace" },
                                                                { 0x33d0, "lmsquare" },
                                                                { 0x0e2c, "lochulathai" },
                                                                { 0x2227, "logicaland" },
                                                                { 0x00ac, "logicalnot" },
                                                                { 0x2310, "logicalnotreversed" },
                                                                { 0x2228, "logicalor" },
                                                                { 0x0e25, "lolingthai" },
                                                                { 0x017f, "longs" },
                                                                { 0xfe4e, "lowlinecenterline" },
                                                                { 0x0332, "lowlinecmb" },
                                                                { 0xfe4d, "lowlinedashed" },
                                                                { 0x25ca, "lozenge" },
                                                                { 0x24a7, "lparen" },
                                                                { 0x0142, "lslash" },
                                                                { 0x2113, "lsquare" },
                                                                { 0xf6ee, "lsuperior" },
                                                                { 0x2591, "ltshade" },
                                                                { 0x0e26, "luthai" },
                                                                { 0x098c, "lvocalicbengali" },
                                                                { 0x090c, "lvocalicdeva" },
                                                                { 0x09e2, "lvocalicvowelsignbengali" },
                                                                { 0x0962, "lvocalicvowelsigndeva" },
                                                                { 0x33d3, "lxsquare" },
                                                                { 0x006d, "m" },
                                                                { 0x09ae, "mabengali" },
                                                                { 0x00af, "macron" },
                                                                { 0x0331, "macronbelowcmb" },
                                                                { 0x0304, "macroncmb" },
                                                                { 0x02cd, "macronlowmod" },
                                                                { 0xffe3, "macronmonospace" },
                                                                { 0x1e3f, "macute" },
                                                                { 0x092e, "madeva" },
                                                                { 0x0aae, "magujarati" },
                                                                { 0x0a2e, "magurmukhi" },
                                                                { 0x05a4, "mahapakhhebrew" },
                                                                { 0x05a4, "mahapakhlefthebrew" },
                                                                { 0x307e, "mahiragana" },
                                                                { 0xf895, "maichattawalowleftthai" },
                                                                { 0xf894, "maichattawalowrightthai" },
                                                                { 0x0e4b, "maichattawathai" },
                                                                { 0xf893, "maichattawaupperleftthai" },
                                                                { 0xf88c, "maieklowleftthai" },
                                                                { 0xf88b, "maieklowrightthai" },
                                                                { 0x0e48, "maiekthai" },
                                                                { 0xf88a, "maiekupperleftthai" },
                                                                { 0xf884, "maihanakatleftthai" },
                                                                { 0x0e31, "maihanakatthai" },
                                                                { 0xf889, "maitaikhuleftthai" },
                                                                { 0x0e47, "maitaikhuthai" },
                                                                { 0xf88f, "maitholowleftthai" },
                                                                { 0xf88e, "maitholowrightthai" },
                                                                { 0x0e49, "maithothai" },
                                                                { 0xf88d, "maithoupperleftthai" },
                                                                { 0xf892, "maitrilowleftthai" },
                                                                { 0xf891, "maitrilowrightthai" },
                                                                { 0x0e4a, "maitrithai" },
                                                                { 0xf890, "maitriupperleftthai" },
                                                                { 0x0e46, "maiyamokthai" },
                                                                { 0x30de, "makatakana" },
                                                                { 0xff8f, "makatakanahalfwidth" },
                                                                { 0x2642, "male" },
                                                                { 0x3347, "mansyonsquare" },
                                                                { 0x05be, "maqafhebrew" },
                                                                { 0x2642, "mars" },
                                                                { 0x05af, "masoracirclehebrew" },
                                                                { 0x3383, "masquare" },
                                                                { 0x3107, "mbopomofo" },
                                                                { 0x33d4, "mbsquare" },
                                                                { 0x24dc, "mcircle" },
                                                                { 0x33a5, "mcubedsquare" },
                                                                { 0x1e41, "mdotaccent" },
                                                                { 0x1e43, "mdotbelow" },
                                                                { 0x0645, "meemarabic" },
                                                                { 0xfee2, "meemfinalarabic" },
                                                                { 0xfee3, "meeminitialarabic" },
                                                                { 0xfee4, "meemmedialarabic" },
                                                                { 0xfcd1, "meemmeeminitialarabic" },
                                                                { 0xfc48, "meemmeemisolatedarabic" },
                                                                { 0x334d, "meetorusquare" },
                                                                { 0x3081, "mehiragana" },
                                                                { 0x337e, "meizierasquare" },
                                                                { 0x30e1, "mekatakana" },
                                                                { 0xff92, "mekatakanahalfwidth" },
                                                                { 0x05de, "mem" },
                                                                { 0xfb3e, "memdagesh" },
                                                                { 0xfb3e, "memdageshhebrew" },
                                                                { 0x05de, "memhebrew" },
                                                                { 0x0574, "menarmenian" },
                                                                { 0x05a5, "merkhahebrew" },
                                                                { 0x05a6, "merkhakefulahebrew" },
                                                                { 0x05a6, "merkhakefulalefthebrew" },
                                                                { 0x05a5, "merkhalefthebrew" },
                                                                { 0x0271, "mhook" },
                                                                { 0x3392, "mhzsquare" },
                                                                { 0xff65, "middledotkatakanahalfwidth" },
                                                                { 0x00b7, "middot" },
                                                                { 0x3272, "mieumacirclekorean" },
                                                                { 0x3212, "mieumaparenkorean" },
                                                                { 0x3264, "mieumcirclekorean" },
                                                                { 0x3141, "mieumkorean" },
                                                                { 0x3170, "mieumpansioskorean" },
                                                                { 0x3204, "mieumparenkorean" },
                                                                { 0x316e, "mieumpieupkorean" },
                                                                { 0x316f, "mieumsioskorean" },
                                                                { 0x307f, "mihiragana" },
                                                                { 0x30df, "mikatakana" },
                                                                { 0xff90, "mikatakanahalfwidth" },
                                                                { 0x2212, "minus" },
                                                                { 0x0320, "minusbelowcmb" },
                                                                { 0x2296, "minuscircle" },
                                                                { 0x02d7, "minusmod" },
                                                                { 0x2213, "minusplus" },
                                                                { 0x2032, "minute" },
                                                                { 0x334a, "miribaarusquare" },
                                                                { 0x3349, "mirisquare" },
                                                                { 0x0270, "mlonglegturned" },
                                                                { 0x3396, "mlsquare" },
                                                                { 0x33a3, "mmcubedsquare" },
                                                                { 0xff4d, "mmonospace" },
                                                                { 0x339f, "mmsquaredsquare" },
                                                                { 0x3082, "mohiragana" },
                                                                { 0x33c1, "mohmsquare" },
                                                                { 0x30e2, "mokatakana" },
                                                                { 0xff93, "mokatakanahalfwidth" },
                                                                { 0x33d6, "molsquare" },
                                                                { 0x0e21, "momathai" },
                                                                { 0x33a7, "moverssquare" },
                                                                { 0x33a8, "moverssquaredsquare" },
                                                                { 0x24a8, "mparen" },
                                                                { 0x33ab, "mpasquare" },
                                                                { 0x33b3, "mssquare" },
                                                                { 0xf6ef, "msuperior" },
                                                                { 0x026f, "mturned" },
                                                                { 0x00b5, "mu" },
                                                                { 0x00b5, "mu1" },
                                                                { 0x3382, "muasquare" },
                                                                { 0x226b, "muchgreater" },
                                                                { 0x226a, "muchless" },
                                                                { 0x338c, "mufsquare" },
                                                                { 0x03bc, "mugreek" },
                                                                { 0x338d, "mugsquare" },
                                                                { 0x3080, "muhiragana" },
                                                                { 0x30e0, "mukatakana" },
                                                                { 0xff91, "mukatakanahalfwidth" },
                                                                { 0x3395, "mulsquare" },
                                                                { 0x00d7, "multiply" },
                                                                { 0x339b, "mumsquare" },
                                                                { 0x05a3, "munahhebrew" },
                                                                { 0x05a3, "munahlefthebrew" },
                                                                { 0x266a, "musicalnote" },
                                                                { 0x266b, "musicalnotedbl" },
                                                                { 0x266d, "musicflatsign" },
                                                                { 0x266f, "musicsharpsign" },
                                                                { 0x33b2, "mussquare" },
                                                                { 0x33b6, "muvsquare" },
                                                                { 0x33bc, "muwsquare" },
                                                                { 0x33b9, "mvmegasquare" },
                                                                { 0x33b7, "mvsquare" },
                                                                { 0x33bf, "mwmegasquare" },
                                                                { 0x33bd, "mwsquare" },
                                                                { 0x006e, "n" },
                                                                { 0x09a8, "nabengali" },
                                                                { 0x2207, "nabla" },
                                                                { 0x0144, "nacute" },
                                                                { 0x0928, "nadeva" },
                                                                { 0x0aa8, "nagujarati" },
                                                                { 0x0a28, "nagurmukhi" },
                                                                { 0x306a, "nahiragana" },
                                                                { 0x30ca, "nakatakana" },
                                                                { 0xff85, "nakatakanahalfwidth" },
                                                                { 0x0149, "napostrophe" },
                                                                { 0x3381, "nasquare" },
                                                                { 0x310b, "nbopomofo" },
                                                                { 0x00a0, "nbspace" },
                                                                { 0x0148, "ncaron" },
                                                                { 0x0146, "ncedilla" },
                                                                { 0x24dd, "ncircle" },
                                                                { 0x1e4b, "ncircumflexbelow" },
                                                                { 0x0146, "ncommaaccent" },
                                                                { 0x1e45, "ndotaccent" },
                                                                { 0x1e47, "ndotbelow" },
                                                                { 0x306d, "nehiragana" },
                                                                { 0x30cd, "nekatakana" },
                                                                { 0xff88, "nekatakanahalfwidth" },
                                                                { 0x20aa, "newsheqelsign" },
                                                                { 0x338b, "nfsquare" },
                                                                { 0x0999, "ngabengali" },
                                                                { 0x0919, "ngadeva" },
                                                                { 0x0a99, "ngagujarati" },
                                                                { 0x0a19, "ngagurmukhi" },
                                                                { 0x0e07, "ngonguthai" },
                                                                { 0x3093, "nhiragana" },
                                                                { 0x0272, "nhookleft" },
                                                                { 0x0273, "nhookretroflex" },
                                                                { 0x326f, "nieunacirclekorean" },
                                                                { 0x320f, "nieunaparenkorean" },
                                                                { 0x3135, "nieuncieuckorean" },
                                                                { 0x3261, "nieuncirclekorean" },
                                                                { 0x3136, "nieunhieuhkorean" },
                                                                { 0x3134, "nieunkorean" },
                                                                { 0x3168, "nieunpansioskorean" },
                                                                { 0x3201, "nieunparenkorean" },
                                                                { 0x3167, "nieunsioskorean" },
                                                                { 0x3166, "nieuntikeutkorean" },
                                                                { 0x306b, "nihiragana" },
                                                                { 0x30cb, "nikatakana" },
                                                                { 0xff86, "nikatakanahalfwidth" },
                                                                { 0xf899, "nikhahitleftthai" },
                                                                { 0x0e4d, "nikhahitthai" },
                                                                { 0x0039, "nine" },
                                                                { 0x0669, "ninearabic" },
                                                                { 0x09ef, "ninebengali" },
                                                                { 0x2468, "ninecircle" },
                                                                { 0x2792, "ninecircleinversesansserif" },
                                                                { 0x096f, "ninedeva" },
                                                                { 0x0aef, "ninegujarati" },
                                                                { 0x0a6f, "ninegurmukhi" },
                                                                { 0x0669, "ninehackarabic" },
                                                                { 0x3029, "ninehangzhou" },
                                                                { 0x3228, "nineideographicparen" },
                                                                { 0x2089, "nineinferior" },
                                                                { 0xff19, "ninemonospace" },
                                                                { 0xf739, "nineoldstyle" },
                                                                { 0x247c, "nineparen" },
                                                                { 0x2490, "nineperiod" },
                                                                { 0x06f9, "ninepersian" },
                                                                { 0x2178, "nineroman" },
                                                                { 0x2079, "ninesuperior" },
                                                                { 0x2472, "nineteencircle" },
                                                                { 0x2486, "nineteenparen" },
                                                                { 0x249a, "nineteenperiod" },
                                                                { 0x0e59, "ninethai" },
                                                                { 0x01cc, "nj" },
                                                                { 0x045a, "njecyrillic" },
                                                                { 0x30f3, "nkatakana" },
                                                                { 0xff9d, "nkatakanahalfwidth" },
                                                                { 0x019e, "nlegrightlong" },
                                                                { 0x1e49, "nlinebelow" },
                                                                { 0xff4e, "nmonospace" },
                                                                { 0x339a, "nmsquare" },
                                                                { 0x09a3, "nnabengali" },
                                                                { 0x0923, "nnadeva" },
                                                                { 0x0aa3, "nnagujarati" },
                                                                { 0x0a23, "nnagurmukhi" },
                                                                { 0x0929, "nnnadeva" },
                                                                { 0x306e, "nohiragana" },
                                                                { 0x30ce, "nokatakana" },
                                                                { 0xff89, "nokatakanahalfwidth" },
                                                                { 0x00a0, "nonbreakingspace" },
                                                                { 0x0e13, "nonenthai" },
                                                                { 0x0e19, "nonuthai" },
                                                                { 0x0646, "noonarabic" },
                                                                { 0xfee6, "noonfinalarabic" },
                                                                { 0x06ba, "noonghunnaarabic" },
                                                                { 0xfb9f, "noonghunnafinalarabic" },
                                                                { 0xfee7, "nooninitialarabic" },
                                                                { 0xfcd2, "noonjeeminitialarabic" },
                                                                { 0xfc4b, "noonjeemisolatedarabic" },
                                                                { 0xfee8, "noonmedialarabic" },
                                                                { 0xfcd5, "noonmeeminitialarabic" },
                                                                { 0xfc4e, "noonmeemisolatedarabic" },
                                                                { 0xfc8d, "noonnoonfinalarabic" },
                                                                { 0x220c, "notcontains" },
                                                                { 0x2209, "notelement" },
                                                                { 0x2209, "notelementof" },
                                                                { 0x2260, "notequal" },
                                                                { 0x226f, "notgreater" },
                                                                { 0x2271, "notgreaternorequal" },
                                                                { 0x2279, "notgreaternorless" },
                                                                { 0x2262, "notidentical" },
                                                                { 0x226e, "notless" },
                                                                { 0x2270, "notlessnorequal" },
                                                                { 0x2226, "notparallel" },
                                                                { 0x2280, "notprecedes" },
                                                                { 0x2284, "notsubset" },
                                                                { 0x2281, "notsucceeds" },
                                                                { 0x2285, "notsuperset" },
                                                                { 0x0576, "nowarmenian" },
                                                                { 0x24a9, "nparen" },
                                                                { 0x33b1, "nssquare" },
                                                                { 0x207f, "nsuperior" },
                                                                { 0x00f1, "ntilde" },
                                                                { 0x03bd, "nu" },
                                                                { 0x306c, "nuhiragana" },
                                                                { 0x30cc, "nukatakana" },
                                                                { 0xff87, "nukatakanahalfwidth" },
                                                                { 0x09bc, "nuktabengali" },
                                                                { 0x093c, "nuktadeva" },
                                                                { 0x0abc, "nuktagujarati" },
                                                                { 0x0a3c, "nuktagurmukhi" },
                                                                { 0x0023, "numbersign" },
                                                                { 0xff03, "numbersignmonospace" },
                                                                { 0xfe5f, "numbersignsmall" },
                                                                { 0x0374, "numeralsigngreek" },
                                                                { 0x0375, "numeralsignlowergreek" },
                                                                { 0x2116, "numero" },
                                                                { 0x05e0, "nun" },
                                                                { 0xfb40, "nundagesh" },
                                                                { 0xfb40, "nundageshhebrew" },
                                                                { 0x05e0, "nunhebrew" },
                                                                { 0x33b5, "nvsquare" },
                                                                { 0x33bb, "nwsquare" },
                                                                { 0x099e, "nyabengali" },
                                                                { 0x091e, "nyadeva" },
                                                                { 0x0a9e, "nyagujarati" },
                                                                { 0x0a1e, "nyagurmukhi" },
                                                                { 0x006f, "o" },
                                                                { 0x00f3, "oacute" },
                                                                { 0x0e2d, "oangthai" },
                                                                { 0x0275, "obarred" },
                                                                { 0x04e9, "obarredcyrillic" },
                                                                { 0x04eb, "obarreddieresiscyrillic" },
                                                                { 0x0993, "obengali" },
                                                                { 0x311b, "obopomofo" },
                                                                { 0x014f, "obreve" },
                                                                { 0x0911, "ocandradeva" },
                                                                { 0x0a91, "ocandragujarati" },
                                                                { 0x0949, "ocandravowelsigndeva" },
                                                                { 0x0ac9, "ocandravowelsigngujarati" },
                                                                { 0x01d2, "ocaron" },
                                                                { 0x24de, "ocircle" },
                                                                { 0x00f4, "ocircumflex" },
                                                                { 0x1ed1, "ocircumflexacute" },
                                                                { 0x1ed9, "ocircumflexdotbelow" },
                                                                { 0x1ed3, "ocircumflexgrave" },
                                                                { 0x1ed5, "ocircumflexhookabove" },
                                                                { 0x1ed7, "ocircumflextilde" },
                                                                { 0x043e, "ocyrillic" },
                                                                { 0x0151, "odblacute" },
                                                                { 0x020d, "odblgrave" },
                                                                { 0x0913, "odeva" },
                                                                { 0x00f6, "odieresis" },
                                                                { 0x04e7, "odieresiscyrillic" },
                                                                { 0x1ecd, "odotbelow" },
                                                                { 0x0153, "oe" },
                                                                { 0x315a, "oekorean" },
                                                                { 0x02db, "ogonek" },
                                                                { 0x0328, "ogonekcmb" },
                                                                { 0x00f2, "ograve" },
                                                                { 0x0a93, "ogujarati" },
                                                                { 0x0585, "oharmenian" },
                                                                { 0x304a, "ohiragana" },
                                                                { 0x1ecf, "ohookabove" },
                                                                { 0x01a1, "ohorn" },
                                                                { 0x1edb, "ohornacute" },
                                                                { 0x1ee3, "ohorndotbelow" },
                                                                { 0x1edd, "ohorngrave" },
                                                                { 0x1edf, "ohornhookabove" },
                                                                { 0x1ee1, "ohorntilde" },
                                                                { 0x0151, "ohungarumlaut" },
                                                                { 0x01a3, "oi" },
                                                                { 0x020f, "oinvertedbreve" },
                                                                { 0x30aa, "okatakana" },
                                                                { 0xff75, "okatakanahalfwidth" },
                                                                { 0x3157, "okorean" },
                                                                { 0x05ab, "olehebrew" },
                                                                { 0x014d, "omacron" },
                                                                { 0x1e53, "omacronacute" },
                                                                { 0x1e51, "omacrongrave" },
                                                                { 0x0950, "omdeva" },
                                                                { 0x03c9, "omega" },
                                                                { 0x03d6, "omega1" },
                                                                { 0x0461, "omegacyrillic" },
                                                                { 0x0277, "omegalatinclosed" },
                                                                { 0x047b, "omegaroundcyrillic" },
                                                                { 0x047d, "omegatitlocyrillic" },
                                                                { 0x03ce, "omegatonos" },
                                                                { 0x0ad0, "omgujarati" },
                                                                { 0x03bf, "omicron" },
                                                                { 0x03cc, "omicrontonos" },
                                                                { 0xff4f, "omonospace" },
                                                                { 0x0031, "one" },
                                                                { 0x0661, "onearabic" },
                                                                { 0x09e7, "onebengali" },
                                                                { 0x2460, "onecircle" },
                                                                { 0x278a, "onecircleinversesansserif" },
                                                                { 0x0967, "onedeva" },
                                                                { 0x2024, "onedotenleader" },
                                                                { 0x215b, "oneeighth" },
                                                                { 0xf6dc, "onefitted" },
                                                                { 0x0ae7, "onegujarati" },
                                                                { 0x0a67, "onegurmukhi" },
                                                                { 0x0661, "onehackarabic" },
                                                                { 0x00bd, "onehalf" },
                                                                { 0x3021, "onehangzhou" },
                                                                { 0x3220, "oneideographicparen" },
                                                                { 0x2081, "oneinferior" },
                                                                { 0xff11, "onemonospace" },
                                                                { 0x09f4, "onenumeratorbengali" },
                                                                { 0xf731, "oneoldstyle" },
                                                                { 0x2474, "oneparen" },
                                                                { 0x2488, "oneperiod" },
                                                                { 0x06f1, "onepersian" },
                                                                { 0x00bc, "onequarter" },
                                                                { 0x2170, "oneroman" },
                                                                { 0x00b9, "onesuperior" },
                                                                { 0x0e51, "onethai" },
                                                                { 0x2153, "onethird" },
                                                                { 0x01eb, "oogonek" },
                                                                { 0x01ed, "oogonekmacron" },
                                                                { 0x0a13, "oogurmukhi" },
                                                                { 0x0a4b, "oomatragurmukhi" },
                                                                { 0x0254, "oopen" },
                                                                { 0x24aa, "oparen" },
                                                                { 0x25e6, "openbullet" },
                                                                { 0x2325, "option" },
                                                                { 0x00aa, "ordfeminine" },
                                                                { 0x00ba, "ordmasculine" },
                                                                { 0x221f, "orthogonal" },
                                                                { 0x0912, "oshortdeva" },
                                                                { 0x094a, "oshortvowelsigndeva" },
                                                                { 0x00f8, "oslash" },
                                                                { 0x01ff, "oslashacute" },
                                                                { 0x3049, "osmallhiragana" },
                                                                { 0x30a9, "osmallkatakana" },
                                                                { 0xff6b, "osmallkatakanahalfwidth" },
                                                                { 0x01ff, "ostrokeacute" },
                                                                { 0xf6f0, "osuperior" },
                                                                { 0x047f, "otcyrillic" },
                                                                { 0x00f5, "otilde" },
                                                                { 0x1e4d, "otildeacute" },
                                                                { 0x1e4f, "otildedieresis" },
                                                                { 0x3121, "oubopomofo" },
                                                                { 0x203e, "overline" },
                                                                { 0xfe4a, "overlinecenterline" },
                                                                { 0x0305, "overlinecmb" },
                                                                { 0xfe49, "overlinedashed" },
                                                                { 0xfe4c, "overlinedblwavy" },
                                                                { 0xfe4b, "overlinewavy" },
                                                                { 0x00af, "overscore" },
                                                                { 0x09cb, "ovowelsignbengali" },
                                                                { 0x094b, "ovowelsigndeva" },
                                                                { 0x0acb, "ovowelsigngujarati" },
                                                                { 0x0070, "p" },
                                                                { 0x3380, "paampssquare" },
                                                                { 0x332b, "paasentosquare" },
                                                                { 0x09aa, "pabengali" },
                                                                { 0x1e55, "pacute" },
                                                                { 0x092a, "padeva" },
                                                                { 0x21df, "pagedown" },
                                                                { 0x21de, "pageup" },
                                                                { 0x0aaa, "pagujarati" },
                                                                { 0x0a2a, "pagurmukhi" },
                                                                { 0x3071, "pahiragana" },
                                                                { 0x0e2f, "paiyannoithai" },
                                                                { 0x30d1, "pakatakana" },
                                                                { 0x0484, "palatalizationcyrilliccmb" },
                                                                { 0x04c0, "palochkacyrillic" },
                                                                { 0x317f, "pansioskorean" },
                                                                { 0x00b6, "paragraph" },
                                                                { 0x2225, "parallel" },
                                                                { 0x0028, "parenleft" },
                                                                { 0xfd3e, "parenleftaltonearabic" },
                                                                { 0xf8ed, "parenleftbt" },
                                                                { 0xf8ec, "parenleftex" },
                                                                { 0x208d, "parenleftinferior" },
                                                                { 0xff08, "parenleftmonospace" },
                                                                { 0xfe59, "parenleftsmall" },
                                                                { 0x207d, "parenleftsuperior" },
                                                                { 0xf8eb, "parenlefttp" },
                                                                { 0xfe35, "parenleftvertical" },
                                                                { 0x0029, "parenright" },
                                                                { 0xfd3f, "parenrightaltonearabic" },
                                                                { 0xf8f8, "parenrightbt" },
                                                                { 0xf8f7, "parenrightex" },
                                                                { 0x208e, "parenrightinferior" },
                                                                { 0xff09, "parenrightmonospace" },
                                                                { 0xfe5a, "parenrightsmall" },
                                                                { 0x207e, "parenrightsuperior" },
                                                                { 0xf8f6, "parenrighttp" },
                                                                { 0xfe36, "parenrightvertical" },
                                                                { 0x2202, "partialdiff" },
                                                                { 0x05c0, "paseqhebrew" },
                                                                { 0x0599, "pashtahebrew" },
                                                                { 0x33a9, "pasquare" },
                                                                { 0x05b7, "patah" },
                                                                { 0x05b7, "patah11" },
                                                                { 0x05b7, "patah1d" },
                                                                { 0x05b7, "patah2a" },
                                                                { 0x05b7, "patahhebrew" },
                                                                { 0x05b7, "patahnarrowhebrew" },
                                                                { 0x05b7, "patahquarterhebrew" },
                                                                { 0x05b7, "patahwidehebrew" },
                                                                { 0x05a1, "pazerhebrew" },
                                                                { 0x3106, "pbopomofo" },
                                                                { 0x24df, "pcircle" },
                                                                { 0x1e57, "pdotaccent" },
                                                                { 0x05e4, "pe" },
                                                                { 0x043f, "pecyrillic" },
                                                                { 0xfb44, "pedagesh" },
                                                                { 0xfb44, "pedageshhebrew" },
                                                                { 0x333b, "peezisquare" },
                                                                { 0xfb43, "pefinaldageshhebrew" },
                                                                { 0x067e, "peharabic" },
                                                                { 0x057a, "peharmenian" },
                                                                { 0x05e4, "pehebrew" },
                                                                { 0xfb57, "pehfinalarabic" },
                                                                { 0xfb58, "pehinitialarabic" },
                                                                { 0x307a, "pehiragana" },
                                                                { 0xfb59, "pehmedialarabic" },
                                                                { 0x30da, "pekatakana" },
                                                                { 0x04a7, "pemiddlehookcyrillic" },
                                                                { 0xfb4e, "perafehebrew" },
                                                                { 0x0025, "percent" },
                                                                { 0x066a, "percentarabic" },
                                                                { 0xff05, "percentmonospace" },
                                                                { 0xfe6a, "percentsmall" },
                                                                { 0x002e, "period" },
                                                                { 0x0589, "periodarmenian" },
                                                                { 0x00b7, "periodcentered" },
                                                                { 0xff61, "periodhalfwidth" },
                                                                { 0xf6e7, "periodinferior" },
                                                                { 0xff0e, "periodmonospace" },
                                                                { 0xfe52, "periodsmall" },
                                                                { 0xf6e8, "periodsuperior" },
                                                                { 0x0342, "perispomenigreekcmb" },
                                                                { 0x22a5, "perpendicular" },
                                                                { 0x2030, "perthousand" },
                                                                { 0x20a7, "peseta" },
                                                                { 0x338a, "pfsquare" },
                                                                { 0x09ab, "phabengali" },
                                                                { 0x092b, "phadeva" },
                                                                { 0x0aab, "phagujarati" },
                                                                { 0x0a2b, "phagurmukhi" },
                                                                { 0x03c6, "phi" },
                                                                { 0x03d5, "phi1" },
                                                                { 0x327a, "phieuphacirclekorean" },
                                                                { 0x321a, "phieuphaparenkorean" },
                                                                { 0x326c, "phieuphcirclekorean" },
                                                                { 0x314d, "phieuphkorean" },
                                                                { 0x320c, "phieuphparenkorean" },
                                                                { 0x0278, "philatin" },
                                                                { 0x0e3a, "phinthuthai" },
                                                                { 0x03d5, "phisymbolgreek" },
                                                                { 0x01a5, "phook" },
                                                                { 0x0e1e, "phophanthai" },
                                                                { 0x0e1c, "phophungthai" },
                                                                { 0x0e20, "phosamphaothai" },
                                                                { 0x03c0, "pi" },
                                                                { 0x3273, "pieupacirclekorean" },
                                                                { 0x3213, "pieupaparenkorean" },
                                                                { 0x3176, "pieupcieuckorean" },
                                                                { 0x3265, "pieupcirclekorean" },
                                                                { 0x3172, "pieupkiyeokkorean" },
                                                                { 0x3142, "pieupkorean" },
                                                                { 0x3205, "pieupparenkorean" },
                                                                { 0x3174, "pieupsioskiyeokkorean" },
                                                                { 0x3144, "pieupsioskorean" },
                                                                { 0x3175, "pieupsiostikeutkorean" },
                                                                { 0x3177, "pieupthieuthkorean" },
                                                                { 0x3173, "pieuptikeutkorean" },
                                                                { 0x3074, "pihiragana" },
                                                                { 0x30d4, "pikatakana" },
                                                                { 0x03d6, "pisymbolgreek" },
                                                                { 0x0583, "piwrarmenian" },
                                                                { 0x002b, "plus" },
                                                                { 0x031f, "plusbelowcmb" },
                                                                { 0x2295, "pluscircle" },
                                                                { 0x00b1, "plusminus" },
                                                                { 0x02d6, "plusmod" },
                                                                { 0xff0b, "plusmonospace" },
                                                                { 0xfe62, "plussmall" },
                                                                { 0x207a, "plussuperior" },
                                                                { 0xff50, "pmonospace" },
                                                                { 0x33d8, "pmsquare" },
                                                                { 0x307d, "pohiragana" },
                                                                { 0x261f, "pointingindexdownwhite" },
                                                                { 0x261c, "pointingindexleftwhite" },
                                                                { 0x261e, "pointingindexrightwhite" },
                                                                { 0x261d, "pointingindexupwhite" },
                                                                { 0x30dd, "pokatakana" },
                                                                { 0x0e1b, "poplathai" },
                                                                { 0x3012, "postalmark" },
                                                                { 0x3020, "postalmarkface" },
                                                                { 0x24ab, "pparen" },
                                                                { 0x227a, "precedes" },
                                                                { 0x211e, "prescription" },
                                                                { 0x02b9, "primemod" },
                                                                { 0x2035, "primereversed" },
                                                                { 0x220f, "product" },
                                                                { 0x2305, "projective" },
                                                                { 0x30fc, "prolongedkana" },
                                                                { 0x2318, "propellor" },
                                                                { 0x2282, "propersubset" },
                                                                { 0x2283, "propersuperset" },
                                                                { 0x2237, "proportion" },
                                                                { 0x221d, "proportional" },
                                                                { 0x03c8, "psi" },
                                                                { 0x0471, "psicyrillic" },
                                                                { 0x0486, "psilipneumatacyrilliccmb" },
                                                                { 0x33b0, "pssquare" },
                                                                { 0x3077, "puhiragana" },
                                                                { 0x30d7, "pukatakana" },
                                                                { 0x33b4, "pvsquare" },
                                                                { 0x33ba, "pwsquare" },
                                                                { 0x0071, "q" },
                                                                { 0x0958, "qadeva" },
                                                                { 0x05a8, "qadmahebrew" },
                                                                { 0x0642, "qafarabic" },
                                                                { 0xfed6, "qaffinalarabic" },
                                                                { 0xfed7, "qafinitialarabic" },
                                                                { 0xfed8, "qafmedialarabic" },
                                                                { 0x05b8, "qamats" },
                                                                { 0x05b8, "qamats10" },
                                                                { 0x05b8, "qamats1a" },
                                                                { 0x05b8, "qamats1c" },
                                                                { 0x05b8, "qamats27" },
                                                                { 0x05b8, "qamats29" },
                                                                { 0x05b8, "qamats33" },
                                                                { 0x05b8, "qamatsde" },
                                                                { 0x05b8, "qamatshebrew" },
                                                                { 0x05b8, "qamatsnarrowhebrew" },
                                                                { 0x05b8, "qamatsqatanhebrew" },
                                                                { 0x05b8, "qamatsqatannarrowhebrew" },
                                                                { 0x05b8, "qamatsqatanquarterhebrew" },
                                                                { 0x05b8, "qamatsqatanwidehebrew" },
                                                                { 0x05b8, "qamatsquarterhebrew" },
                                                                { 0x05b8, "qamatswidehebrew" },
                                                                { 0x059f, "qarneyparahebrew" },
                                                                { 0x3111, "qbopomofo" },
                                                                { 0x24e0, "qcircle" },
                                                                { 0x02a0, "qhook" },
                                                                { 0xff51, "qmonospace" },
                                                                { 0x05e7, "qof" },
                                                                { 0xfb47, "qofdagesh" },
                                                                { 0xfb47, "qofdageshhebrew" },
                                                                { 0x05e7, "qofhebrew" },
                                                                { 0x24ac, "qparen" },
                                                                { 0x2669, "quarternote" },
                                                                { 0x05bb, "qubuts" },
                                                                { 0x05bb, "qubuts18" },
                                                                { 0x05bb, "qubuts25" },
                                                                { 0x05bb, "qubuts31" },
                                                                { 0x05bb, "qubutshebrew" },
                                                                { 0x05bb, "qubutsnarrowhebrew" },
                                                                { 0x05bb, "qubutsquarterhebrew" },
                                                                { 0x05bb, "qubutswidehebrew" },
                                                                { 0x003f, "question" },
                                                                { 0x061f, "questionarabic" },
                                                                { 0x055e, "questionarmenian" },
                                                                { 0x00bf, "questiondown" },
                                                                { 0xf7bf, "questiondownsmall" },
                                                                { 0x037e, "questiongreek" },
                                                                { 0xff1f, "questionmonospace" },
                                                                { 0xf73f, "questionsmall" },
                                                                { 0x0022, "quotedbl" },
                                                                { 0x201e, "quotedblbase" },
                                                                { 0x201c, "quotedblleft" },
                                                                { 0xff02, "quotedblmonospace" },
                                                                { 0x301e, "quotedblprime" },
                                                                { 0x301d, "quotedblprimereversed" },
                                                                { 0x201d, "quotedblright" },
                                                                { 0x2018, "quoteleft" },
                                                                { 0x201b, "quoteleftreversed" },
                                                                { 0x201b, "quotereversed" },
                                                                { 0x2019, "quoteright" },
                                                                { 0x0149, "quoterightn" },
                                                                { 0x201a, "quotesinglbase" },
                                                                { 0x0027, "quotesingle" },
                                                                { 0xff07, "quotesinglemonospace" },
                                                                { 0x0072, "r" },
                                                                { 0x057c, "raarmenian" },
                                                                { 0x09b0, "rabengali" },
                                                                { 0x0155, "racute" },
                                                                { 0x0930, "radeva" },
                                                                { 0x221a, "radical" },
                                                                { 0xf8e5, "radicalex" },
                                                                { 0x33ae, "radoverssquare" },
                                                                { 0x33af, "radoverssquaredsquare" },
                                                                { 0x33ad, "radsquare" },
                                                                { 0x05bf, "rafe" },
                                                                { 0x05bf, "rafehebrew" },
                                                                { 0x0ab0, "ragujarati" },
                                                                { 0x0a30, "ragurmukhi" },
                                                                { 0x3089, "rahiragana" },
                                                                { 0x30e9, "rakatakana" },
                                                                { 0xff97, "rakatakanahalfwidth" },
                                                                { 0x09f1, "ralowerdiagonalbengali" },
                                                                { 0x09f0, "ramiddlediagonalbengali" },
                                                                { 0x0264, "ramshorn" },
                                                                { 0x2236, "ratio" },
                                                                { 0x3116, "rbopomofo" },
                                                                { 0x0159, "rcaron" },
                                                                { 0x0157, "rcedilla" },
                                                                { 0x24e1, "rcircle" },
                                                                { 0x0157, "rcommaaccent" },
                                                                { 0x0211, "rdblgrave" },
                                                                { 0x1e59, "rdotaccent" },
                                                                { 0x1e5b, "rdotbelow" },
                                                                { 0x1e5d, "rdotbelowmacron" },
                                                                { 0x203b, "referencemark" },
                                                                { 0x2286, "reflexsubset" },
                                                                { 0x2287, "reflexsuperset" },
                                                                { 0x00ae, "registered" },
                                                                { 0xf8e8, "registersans" },
                                                                { 0xf6da, "registerserif" },
                                                                { 0x0631, "reharabic" },
                                                                { 0x0580, "reharmenian" },
                                                                { 0xfeae, "rehfinalarabic" },
                                                                { 0x308c, "rehiragana" },
                                                                { 0x30ec, "rekatakana" },
                                                                { 0xff9a, "rekatakanahalfwidth" },
                                                                { 0x05e8, "resh" },
                                                                { 0xfb48, "reshdageshhebrew" },
                                                                { 0x05e8, "reshhebrew" },
                                                                { 0x223d, "reversedtilde" },
                                                                { 0x0597, "reviahebrew" },
                                                                { 0x0597, "reviamugrashhebrew" },
                                                                { 0x2310, "revlogicalnot" },
                                                                { 0x027e, "rfishhook" },
                                                                { 0x027f, "rfishhookreversed" },
                                                                { 0x09dd, "rhabengali" },
                                                                { 0x095d, "rhadeva" },
                                                                { 0x03c1, "rho" },
                                                                { 0x027d, "rhook" },
                                                                { 0x027b, "rhookturned" },
                                                                { 0x02b5, "rhookturnedsuperior" },
                                                                { 0x03f1, "rhosymbolgreek" },
                                                                { 0x02de, "rhotichookmod" },
                                                                { 0x3271, "rieulacirclekorean" },
                                                                { 0x3211, "rieulaparenkorean" },
                                                                { 0x3263, "rieulcirclekorean" },
                                                                { 0x3140, "rieulhieuhkorean" },
                                                                { 0x313a, "rieulkiyeokkorean" },
                                                                { 0x3169, "rieulkiyeoksioskorean" },
                                                                { 0x3139, "rieulkorean" },
                                                                { 0x313b, "rieulmieumkorean" },
                                                                { 0x316c, "rieulpansioskorean" },
                                                                { 0x3203, "rieulparenkorean" },
                                                                { 0x313f, "rieulphieuphkorean" },
                                                                { 0x313c, "rieulpieupkorean" },
                                                                { 0x316b, "rieulpieupsioskorean" },
                                                                { 0x313d, "rieulsioskorean" },
                                                                { 0x313e, "rieulthieuthkorean" },
                                                                { 0x316a, "rieultikeutkorean" },
                                                                { 0x316d, "rieulyeorinhieuhkorean" },
                                                                { 0x221f, "rightangle" },
                                                                { 0x0319, "righttackbelowcmb" },
                                                                { 0x22bf, "righttriangle" },
                                                                { 0x308a, "rihiragana" },
                                                                { 0x30ea, "rikatakana" },
                                                                { 0xff98, "rikatakanahalfwidth" },
                                                                { 0x02da, "ring" },
                                                                { 0x0325, "ringbelowcmb" },
                                                                { 0x030a, "ringcmb" },
                                                                { 0x02bf, "ringhalfleft" },
                                                                { 0x0559, "ringhalfleftarmenian" },
                                                                { 0x031c, "ringhalfleftbelowcmb" },
                                                                { 0x02d3, "ringhalfleftcentered" },
                                                                { 0x02be, "ringhalfright" },
                                                                { 0x0339, "ringhalfrightbelowcmb" },
                                                                { 0x02d2, "ringhalfrightcentered" },
                                                                { 0x0213, "rinvertedbreve" },
                                                                { 0x3351, "rittorusquare" },
                                                                { 0x1e5f, "rlinebelow" },
                                                                { 0x027c, "rlongleg" },
                                                                { 0x027a, "rlonglegturned" },
                                                                { 0xff52, "rmonospace" },
                                                                { 0x308d, "rohiragana" },
                                                                { 0x30ed, "rokatakana" },
                                                                { 0xff9b, "rokatakanahalfwidth" },
                                                                { 0x0e23, "roruathai" },
                                                                { 0x24ad, "rparen" },
                                                                { 0x09dc, "rrabengali" },
                                                                { 0x0931, "rradeva" },
                                                                { 0x0a5c, "rragurmukhi" },
                                                                { 0x0691, "rreharabic" },
                                                                { 0xfb8d, "rrehfinalarabic" },
                                                                { 0x09e0, "rrvocalicbengali" },
                                                                { 0x0960, "rrvocalicdeva" },
                                                                { 0x0ae0, "rrvocalicgujarati" },
                                                                { 0x09c4, "rrvocalicvowelsignbengali" },
                                                                { 0x0944, "rrvocalicvowelsigndeva" },
                                                                { 0x0ac4, "rrvocalicvowelsigngujarati" },
                                                                { 0xf6f1, "rsuperior" },
                                                                { 0x2590, "rtblock" },
                                                                { 0x0279, "rturned" },
                                                                { 0x02b4, "rturnedsuperior" },
                                                                { 0x308b, "ruhiragana" },
                                                                { 0x30eb, "rukatakana" },
                                                                { 0xff99, "rukatakanahalfwidth" },
                                                                { 0x09f2, "rupeemarkbengali" },
                                                                { 0x09f3, "rupeesignbengali" },
                                                                { 0xf6dd, "rupiah" },
                                                                { 0x0e24, "ruthai" },
                                                                { 0x098b, "rvocalicbengali" },
                                                                { 0x090b, "rvocalicdeva" },
                                                                { 0x0a8b, "rvocalicgujarati" },
                                                                { 0x09c3, "rvocalicvowelsignbengali" },
                                                                { 0x0943, "rvocalicvowelsigndeva" },
                                                                { 0x0ac3, "rvocalicvowelsigngujarati" },
                                                                { 0x0073, "s" },
                                                                { 0x09b8, "sabengali" },
                                                                { 0x015b, "sacute" },
                                                                { 0x1e65, "sacutedotaccent" },
                                                                { 0x0635, "sadarabic" },
                                                                { 0x0938, "sadeva" },
                                                                { 0xfeba, "sadfinalarabic" },
                                                                { 0xfebb, "sadinitialarabic" },
                                                                { 0xfebc, "sadmedialarabic" },
                                                                { 0x0ab8, "sagujarati" },
                                                                { 0x0a38, "sagurmukhi" },
                                                                { 0x3055, "sahiragana" },
                                                                { 0x30b5, "sakatakana" },
                                                                { 0xff7b, "sakatakanahalfwidth" },
                                                                { 0xfdfa, "sallallahoualayhewasallamarabic" },
                                                                { 0x05e1, "samekh" },
                                                                { 0xfb41, "samekhdagesh" },
                                                                { 0xfb41, "samekhdageshhebrew" },
                                                                { 0x05e1, "samekhhebrew" },
                                                                { 0x0e32, "saraaathai" },
                                                                { 0x0e41, "saraaethai" },
                                                                { 0x0e44, "saraaimaimalaithai" },
                                                                { 0x0e43, "saraaimaimuanthai" },
                                                                { 0x0e33, "saraamthai" },
                                                                { 0x0e30, "saraathai" },
                                                                { 0x0e40, "saraethai" },
                                                                { 0xf886, "saraiileftthai" },
                                                                { 0x0e35, "saraiithai" },
                                                                { 0xf885, "saraileftthai" },
                                                                { 0x0e34, "saraithai" },
                                                                { 0x0e42, "saraothai" },
                                                                { 0xf888, "saraueeleftthai" },
                                                                { 0x0e37, "saraueethai" },
                                                                { 0xf887, "saraueleftthai" },
                                                                { 0x0e36, "sarauethai" },
                                                                { 0x0e38, "sarauthai" },
                                                                { 0x0e39, "sarauuthai" },
                                                                { 0x3119, "sbopomofo" },
                                                                { 0x0161, "scaron" },
                                                                { 0x1e67, "scarondotaccent" },
                                                                { 0x015f, "scedilla" },
                                                                { 0x0259, "schwa" },
                                                                { 0x04d9, "schwacyrillic" },
                                                                { 0x04db, "schwadieresiscyrillic" },
                                                                { 0x025a, "schwahook" },
                                                                { 0x24e2, "scircle" },
                                                                { 0x015d, "scircumflex" },
                                                                { 0x0219, "scommaaccent" },
                                                                { 0x1e61, "sdotaccent" },
                                                                { 0x1e63, "sdotbelow" },
                                                                { 0x1e69, "sdotbelowdotaccent" },
                                                                { 0x033c, "seagullbelowcmb" },
                                                                { 0x2033, "second" },
                                                                { 0x02ca, "secondtonechinese" },
                                                                { 0x00a7, "section" },
                                                                { 0x0633, "seenarabic" },
                                                                { 0xfeb2, "seenfinalarabic" },
                                                                { 0xfeb3, "seeninitialarabic" },
                                                                { 0xfeb4, "seenmedialarabic" },
                                                                { 0x05b6, "segol" },
                                                                { 0x05b6, "segol13" },
                                                                { 0x05b6, "segol1f" },
                                                                { 0x05b6, "segol2c" },
                                                                { 0x05b6, "segolhebrew" },
                                                                { 0x05b6, "segolnarrowhebrew" },
                                                                { 0x05b6, "segolquarterhebrew" },
                                                                { 0x0592, "segoltahebrew" },
                                                                { 0x05b6, "segolwidehebrew" },
                                                                { 0x057d, "seharmenian" },
                                                                { 0x305b, "sehiragana" },
                                                                { 0x30bb, "sekatakana" },
                                                                { 0xff7e, "sekatakanahalfwidth" },
                                                                { 0x003b, "semicolon" },
                                                                { 0x061b, "semicolonarabic" },
                                                                { 0xff1b, "semicolonmonospace" },
                                                                { 0xfe54, "semicolonsmall" },
                                                                { 0x309c, "semivoicedmarkkana" },
                                                                { 0xff9f, "semivoicedmarkkanahalfwidth" },
                                                                { 0x3322, "sentisquare" },
                                                                { 0x3323, "sentosquare" },
                                                                { 0x0037, "seven" },
                                                                { 0x0667, "sevenarabic" },
                                                                { 0x09ed, "sevenbengali" },
                                                                { 0x2466, "sevencircle" },
                                                                { 0x2790, "sevencircleinversesansserif" },
                                                                { 0x096d, "sevendeva" },
                                                                { 0x215e, "seveneighths" },
                                                                { 0x0aed, "sevengujarati" },
                                                                { 0x0a6d, "sevengurmukhi" },
                                                                { 0x0667, "sevenhackarabic" },
                                                                { 0x3027, "sevenhangzhou" },
                                                                { 0x3226, "sevenideographicparen" },
                                                                { 0x2087, "seveninferior" },
                                                                { 0xff17, "sevenmonospace" },
                                                                { 0xf737, "sevenoldstyle" },
                                                                { 0x247a, "sevenparen" },
                                                                { 0x248e, "sevenperiod" },
                                                                { 0x06f7, "sevenpersian" },
                                                                { 0x2176, "sevenroman" },
                                                                { 0x2077, "sevensuperior" },
                                                                { 0x2470, "seventeencircle" },
                                                                { 0x2484, "seventeenparen" },
                                                                { 0x2498, "seventeenperiod" },
                                                                { 0x0e57, "seventhai" },
                                                                { 0x00ad, "sfthyphen" },
                                                                { 0x0577, "shaarmenian" },
                                                                { 0x09b6, "shabengali" },
                                                                { 0x0448, "shacyrillic" },
                                                                { 0x0651, "shaddaarabic" },
                                                                { 0xfc61, "shaddadammaarabic" },
                                                                { 0xfc5e, "shaddadammatanarabic" },
                                                                { 0xfc60, "shaddafathaarabic" },
                                                                { 0xfc62, "shaddakasraarabic" },
                                                                { 0xfc5f, "shaddakasratanarabic" },
                                                                { 0x2592, "shade" },
                                                                { 0x2593, "shadedark" },
                                                                { 0x2591, "shadelight" },
                                                                { 0x2592, "shademedium" },
                                                                { 0x0936, "shadeva" },
                                                                { 0x0ab6, "shagujarati" },
                                                                { 0x0a36, "shagurmukhi" },
                                                                { 0x0593, "shalshelethebrew" },
                                                                { 0x3115, "shbopomofo" },
                                                                { 0x0449, "shchacyrillic" },
                                                                { 0x0634, "sheenarabic" },
                                                                { 0xfeb6, "sheenfinalarabic" },
                                                                { 0xfeb7, "sheeninitialarabic" },
                                                                { 0xfeb8, "sheenmedialarabic" },
                                                                { 0x03e3, "sheicoptic" },
                                                                { 0x20aa, "sheqel" },
                                                                { 0x20aa, "sheqelhebrew" },
                                                                { 0x05b0, "sheva" },
                                                                { 0x05b0, "sheva115" },
                                                                { 0x05b0, "sheva15" },
                                                                { 0x05b0, "sheva22" },
                                                                { 0x05b0, "sheva2e" },
                                                                { 0x05b0, "shevahebrew" },
                                                                { 0x05b0, "shevanarrowhebrew" },
                                                                { 0x05b0, "shevaquarterhebrew" },
                                                                { 0x05b0, "shevawidehebrew" },
                                                                { 0x04bb, "shhacyrillic" },
                                                                { 0x03ed, "shimacoptic" },
                                                                { 0x05e9, "shin" },
                                                                { 0xfb49, "shindagesh" },
                                                                { 0xfb49, "shindageshhebrew" },
                                                                { 0xfb2c, "shindageshshindot" },
                                                                { 0xfb2c, "shindageshshindothebrew" },
                                                                { 0xfb2d, "shindageshsindot" },
                                                                { 0xfb2d, "shindageshsindothebrew" },
                                                                { 0x05c1, "shindothebrew" },
                                                                { 0x05e9, "shinhebrew" },
                                                                { 0xfb2a, "shinshindot" },
                                                                { 0xfb2a, "shinshindothebrew" },
                                                                { 0xfb2b, "shinsindot" },
                                                                { 0xfb2b, "shinsindothebrew" },
                                                                { 0x0282, "shook" },
                                                                { 0x03c3, "sigma" },
                                                                { 0x03c2, "sigma1" },
                                                                { 0x03c2, "sigmafinal" },
                                                                { 0x03f2, "sigmalunatesymbolgreek" },
                                                                { 0x3057, "sihiragana" },
                                                                { 0x30b7, "sikatakana" },
                                                                { 0xff7c, "sikatakanahalfwidth" },
                                                                { 0x05bd, "siluqhebrew" },
                                                                { 0x05bd, "siluqlefthebrew" },
                                                                { 0x223c, "similar" },
                                                                { 0x2243, "similarequal" },
                                                                { 0x05c2, "sindothebrew" },
                                                                { 0x3274, "siosacirclekorean" },
                                                                { 0x3214, "siosaparenkorean" },
                                                                { 0x317e, "sioscieuckorean" },
                                                                { 0x3266, "sioscirclekorean" },
                                                                { 0x317a, "sioskiyeokkorean" },
                                                                { 0x3145, "sioskorean" },
                                                                { 0x317b, "siosnieunkorean" },
                                                                { 0x3206, "siosparenkorean" },
                                                                { 0x317d, "siospieupkorean" },
                                                                { 0x317c, "siostikeutkorean" },
                                                                { 0x0036, "six" },
                                                                { 0x0666, "sixarabic" },
                                                                { 0x09ec, "sixbengali" },
                                                                { 0x2465, "sixcircle" },
                                                                { 0x278f, "sixcircleinversesansserif" },
                                                                { 0x096c, "sixdeva" },
                                                                { 0x0aec, "sixgujarati" },
                                                                { 0x0a6c, "sixgurmukhi" },
                                                                { 0x0666, "sixhackarabic" },
                                                                { 0x3026, "sixhangzhou" },
                                                                { 0x3225, "sixideographicparen" },
                                                                { 0x2086, "sixinferior" },
                                                                { 0xff16, "sixmonospace" },
                                                                { 0xf736, "sixoldstyle" },
                                                                { 0x2479, "sixparen" },
                                                                { 0x248d, "sixperiod" },
                                                                { 0x06f6, "sixpersian" },
                                                                { 0x2175, "sixroman" },
                                                                { 0x2076, "sixsuperior" },
                                                                { 0x246f, "sixteencircle" },
                                                                { 0x09f9, "sixteencurrencydenominatorbengali" },
                                                                { 0x2483, "sixteenparen" },
                                                                { 0x2497, "sixteenperiod" },
                                                                { 0x0e56, "sixthai" },
                                                                { 0x002f, "slash" },
                                                                { 0xff0f, "slashmonospace" },
                                                                { 0x017f, "slong" },
                                                                { 0x1e9b, "slongdotaccent" },
                                                                { 0x263a, "smileface" },
                                                                { 0xff53, "smonospace" },
                                                                { 0x05c3, "sofpasuqhebrew" },
                                                                { 0x00ad, "softhyphen" },
                                                                { 0x044c, "softsigncyrillic" },
                                                                { 0x305d, "sohiragana" },
                                                                { 0x30bd, "sokatakana" },
                                                                { 0xff7f, "sokatakanahalfwidth" },
                                                                { 0x0338, "soliduslongoverlaycmb" },
                                                                { 0x0337, "solidusshortoverlaycmb" },
                                                                { 0x0e29, "sorusithai" },
                                                                { 0x0e28, "sosalathai" },
                                                                { 0x0e0b, "sosothai" },
                                                                { 0x0e2a, "sosuathai" },
                                                                { 0x0020, "space" },
                                                                { 0x0020, "spacehackarabic" },
                                                                { 0x2660, "spade" },
                                                                { 0x2660, "spadesuitblack" },
                                                                { 0x2664, "spadesuitwhite" },
                                                                { 0x24ae, "sparen" },
                                                                { 0x033b, "squarebelowcmb" },
                                                                { 0x33c4, "squarecc" },
                                                                { 0x339d, "squarecm" },
                                                                { 0x25a9, "squarediagonalcrosshatchfill" },
                                                                { 0x25a4, "squarehorizontalfill" },
                                                                { 0x338f, "squarekg" },
                                                                { 0x339e, "squarekm" },
                                                                { 0x33ce, "squarekmcapital" },
                                                                { 0x33d1, "squareln" },
                                                                { 0x33d2, "squarelog" },
                                                                { 0x338e, "squaremg" },
                                                                { 0x33d5, "squaremil" },
                                                                { 0x339c, "squaremm" },
                                                                { 0x33a1, "squaremsquared" },
                                                                { 0x25a6, "squareorthogonalcrosshatchfill" },
                                                                { 0x25a7, "squareupperlefttolowerrightfill" },
                                                                { 0x25a8, "squareupperrighttolowerleftfill" },
                                                                { 0x25a5, "squareverticalfill" },
                                                                { 0x25a3, "squarewhitewithsmallblack" },
                                                                { 0x33db, "srsquare" },
                                                                { 0x09b7, "ssabengali" },
                                                                { 0x0937, "ssadeva" },
                                                                { 0x0ab7, "ssagujarati" },
                                                                { 0x3149, "ssangcieuckorean" },
                                                                { 0x3185, "ssanghieuhkorean" },
                                                                { 0x3180, "ssangieungkorean" },
                                                                { 0x3132, "ssangkiyeokkorean" },
                                                                { 0x3165, "ssangnieunkorean" },
                                                                { 0x3143, "ssangpieupkorean" },
                                                                { 0x3146, "ssangsioskorean" },
                                                                { 0x3138, "ssangtikeutkorean" },
                                                                { 0xf6f2, "ssuperior" },
                                                                { 0x00a3, "sterling" },
                                                                { 0xffe1, "sterlingmonospace" },
                                                                { 0x0336, "strokelongoverlaycmb" },
                                                                { 0x0335, "strokeshortoverlaycmb" },
                                                                { 0x2282, "subset" },
                                                                { 0x228a, "subsetnotequal" },
                                                                { 0x2286, "subsetorequal" },
                                                                { 0x227b, "succeeds" },
                                                                { 0x220b, "suchthat" },
                                                                { 0x3059, "suhiragana" },
                                                                { 0x30b9, "sukatakana" },
                                                                { 0xff7d, "sukatakanahalfwidth" },
                                                                { 0x0652, "sukunarabic" },
                                                                { 0x2211, "summation" },
                                                                { 0x263c, "sun" },
                                                                { 0x2283, "superset" },
                                                                { 0x228b, "supersetnotequal" },
                                                                { 0x2287, "supersetorequal" },
                                                                { 0x33dc, "svsquare" },
                                                                { 0x337c, "syouwaerasquare" },
                                                                { 0x0074, "t" },
                                                                { 0x09a4, "tabengali" },
                                                                { 0x22a4, "tackdown" },
                                                                { 0x22a3, "tackleft" },
                                                                { 0x0924, "tadeva" },
                                                                { 0x0aa4, "tagujarati" },
                                                                { 0x0a24, "tagurmukhi" },
                                                                { 0x0637, "taharabic" },
                                                                { 0xfec2, "tahfinalarabic" },
                                                                { 0xfec3, "tahinitialarabic" },
                                                                { 0x305f, "tahiragana" },
                                                                { 0xfec4, "tahmedialarabic" },
                                                                { 0x337d, "taisyouerasquare" },
                                                                { 0x30bf, "takatakana" },
                                                                { 0xff80, "takatakanahalfwidth" },
                                                                { 0x0640, "tatweelarabic" },
                                                                { 0x03c4, "tau" },
                                                                { 0x05ea, "tav" },
                                                                { 0xfb4a, "tavdages" },
                                                                { 0xfb4a, "tavdagesh" },
                                                                { 0xfb4a, "tavdageshhebrew" },
                                                                { 0x05ea, "tavhebrew" },
                                                                { 0x0167, "tbar" },
                                                                { 0x310a, "tbopomofo" },
                                                                { 0x0165, "tcaron" },
                                                                { 0x02a8, "tccurl" },
                                                                { 0x0163, "tcedilla" },
                                                                { 0x0686, "tcheharabic" },
                                                                { 0xfb7b, "tchehfinalarabic" },
                                                                { 0xfb7c, "tchehinitialarabic" },
                                                                { 0xfb7d, "tchehmedialarabic" },
                                                                { 0x24e3, "tcircle" },
                                                                { 0x1e71, "tcircumflexbelow" },
                                                                { 0x0163, "tcommaaccent" },
                                                                { 0x1e97, "tdieresis" },
                                                                { 0x1e6b, "tdotaccent" },
                                                                { 0x1e6d, "tdotbelow" },
                                                                { 0x0442, "tecyrillic" },
                                                                { 0x04ad, "tedescendercyrillic" },
                                                                { 0x062a, "teharabic" },
                                                                { 0xfe96, "tehfinalarabic" },
                                                                { 0xfca2, "tehhahinitialarabic" },
                                                                { 0xfc0c, "tehhahisolatedarabic" },
                                                                { 0xfe97, "tehinitialarabic" },
                                                                { 0x3066, "tehiragana" },
                                                                { 0xfca1, "tehjeeminitialarabic" },
                                                                { 0xfc0b, "tehjeemisolatedarabic" },
                                                                { 0x0629, "tehmarbutaarabic" },
                                                                { 0xfe94, "tehmarbutafinalarabic" },
                                                                { 0xfe98, "tehmedialarabic" },
                                                                { 0xfca4, "tehmeeminitialarabic" },
                                                                { 0xfc0e, "tehmeemisolatedarabic" },
                                                                { 0xfc73, "tehnoonfinalarabic" },
                                                                { 0x30c6, "tekatakana" },
                                                                { 0xff83, "tekatakanahalfwidth" },
                                                                { 0x2121, "telephone" },
                                                                { 0x260e, "telephoneblack" },
                                                                { 0x05a0, "telishagedolahebrew" },
                                                                { 0x05a9, "telishaqetanahebrew" },
                                                                { 0x2469, "tencircle" },
                                                                { 0x3229, "tenideographicparen" },
                                                                { 0x247d, "tenparen" },
                                                                { 0x2491, "tenperiod" },
                                                                { 0x2179, "tenroman" },
                                                                { 0x02a7, "tesh" },
                                                                { 0x05d8, "tet" },
                                                                { 0xfb38, "tetdagesh" },
                                                                { 0xfb38, "tetdageshhebrew" },
                                                                { 0x05d8, "tethebrew" },
                                                                { 0x04b5, "tetsecyrillic" },
                                                                { 0x059b, "tevirhebrew" },
                                                                { 0x059b, "tevirlefthebrew" },
                                                                { 0x09a5, "thabengali" },
                                                                { 0x0925, "thadeva" },
                                                                { 0x0aa5, "thagujarati" },
                                                                { 0x0a25, "thagurmukhi" },
                                                                { 0x0630, "thalarabic" },
                                                                { 0xfeac, "thalfinalarabic" },
                                                                { 0xf898, "thanthakhatlowleftthai" },
                                                                { 0xf897, "thanthakhatlowrightthai" },
                                                                { 0x0e4c, "thanthakhatthai" },
                                                                { 0xf896, "thanthakhatupperleftthai" },
                                                                { 0x062b, "theharabic" },
                                                                { 0xfe9a, "thehfinalarabic" },
                                                                { 0xfe9b, "thehinitialarabic" },
                                                                { 0xfe9c, "thehmedialarabic" },
                                                                { 0x2203, "thereexists" },
                                                                { 0x2234, "therefore" },
                                                                { 0x03b8, "theta" },
                                                                { 0x03d1, "theta1" },
                                                                { 0x03d1, "thetasymbolgreek" },
                                                                { 0x3279, "thieuthacirclekorean" },
                                                                { 0x3219, "thieuthaparenkorean" },
                                                                { 0x326b, "thieuthcirclekorean" },
                                                                { 0x314c, "thieuthkorean" },
                                                                { 0x320b, "thieuthparenkorean" },
                                                                { 0x246c, "thirteencircle" },
                                                                { 0x2480, "thirteenparen" },
                                                                { 0x2494, "thirteenperiod" },
                                                                { 0x0e11, "thonangmonthothai" },
                                                                { 0x01ad, "thook" },
                                                                { 0x0e12, "thophuthaothai" },
                                                                { 0x00fe, "thorn" },
                                                                { 0x0e17, "thothahanthai" },
                                                                { 0x0e10, "thothanthai" },
                                                                { 0x0e18, "thothongthai" },
                                                                { 0x0e16, "thothungthai" },
                                                                { 0x0482, "thousandcyrillic" },
                                                                { 0x066c, "thousandsseparatorarabic" },
                                                                { 0x066c, "thousandsseparatorpersian" },
                                                                { 0x0033, "three" },
                                                                { 0x0663, "threearabic" },
                                                                { 0x09e9, "threebengali" },
                                                                { 0x2462, "threecircle" },
                                                                { 0x278c, "threecircleinversesansserif" },
                                                                { 0x0969, "threedeva" },
                                                                { 0x215c, "threeeighths" },
                                                                { 0x0ae9, "threegujarati" },
                                                                { 0x0a69, "threegurmukhi" },
                                                                { 0x0663, "threehackarabic" },
                                                                { 0x3023, "threehangzhou" },
                                                                { 0x3222, "threeideographicparen" },
                                                                { 0x2083, "threeinferior" },
                                                                { 0xff13, "threemonospace" },
                                                                { 0x09f6, "threenumeratorbengali" },
                                                                { 0xf733, "threeoldstyle" },
                                                                { 0x2476, "threeparen" },
                                                                { 0x248a, "threeperiod" },
                                                                { 0x06f3, "threepersian" },
                                                                { 0x00be, "threequarters" },
                                                                { 0xf6de, "threequartersemdash" },
                                                                { 0x2172, "threeroman" },
                                                                { 0x00b3, "threesuperior" },
                                                                { 0x0e53, "threethai" },
                                                                { 0x3394, "thzsquare" },
                                                                { 0x3061, "tihiragana" },
                                                                { 0x30c1, "tikatakana" },
                                                                { 0xff81, "tikatakanahalfwidth" },
                                                                { 0x3270, "tikeutacirclekorean" },
                                                                { 0x3210, "tikeutaparenkorean" },
                                                                { 0x3262, "tikeutcirclekorean" },
                                                                { 0x3137, "tikeutkorean" },
                                                                { 0x3202, "tikeutparenkorean" },
                                                                { 0x02dc, "tilde" },
                                                                { 0x0330, "tildebelowcmb" },
                                                                { 0x0303, "tildecmb" },
                                                                { 0x0303, "tildecomb" },
                                                                { 0x0360, "tildedoublecmb" },
                                                                { 0x223c, "tildeoperator" },
                                                                { 0x0334, "tildeoverlaycmb" },
                                                                { 0x033e, "tildeverticalcmb" },
                                                                { 0x2297, "timescircle" },
                                                                { 0x0596, "tipehahebrew" },
                                                                { 0x0596, "tipehalefthebrew" },
                                                                { 0x0a70, "tippigurmukhi" },
                                                                { 0x0483, "titlocyrilliccmb" },
                                                                { 0x057f, "tiwnarmenian" },
                                                                { 0x1e6f, "tlinebelow" },
                                                                { 0xff54, "tmonospace" },
                                                                { 0x0569, "toarmenian" },
                                                                { 0x3068, "tohiragana" },
                                                                { 0x30c8, "tokatakana" },
                                                                { 0xff84, "tokatakanahalfwidth" },
                                                                { 0x02e5, "tonebarextrahighmod" },
                                                                { 0x02e9, "tonebarextralowmod" },
                                                                { 0x02e6, "tonebarhighmod" },
                                                                { 0x02e8, "tonebarlowmod" },
                                                                { 0x02e7, "tonebarmidmod" },
                                                                { 0x01bd, "tonefive" },
                                                                { 0x0185, "tonesix" },
                                                                { 0x01a8, "tonetwo" },
                                                                { 0x0384, "tonos" },
                                                                { 0x3327, "tonsquare" },
                                                                { 0x0e0f, "topatakthai" },
                                                                { 0x3014, "tortoiseshellbracketleft" },
                                                                { 0xfe5d, "tortoiseshellbracketleftsmall" },
                                                                { 0xfe39, "tortoiseshellbracketleftvertical" },
                                                                { 0x3015, "tortoiseshellbracketright" },
                                                                { 0xfe5e, "tortoiseshellbracketrightsmall" },
                                                                { 0xfe3a, "tortoiseshellbracketrightvertical" },
                                                                { 0x0e15, "totaothai" },
                                                                { 0x01ab, "tpalatalhook" },
                                                                { 0x24af, "tparen" },
                                                                { 0x2122, "trademark" },
                                                                { 0xf8ea, "trademarksans" },
                                                                { 0xf6db, "trademarkserif" },
                                                                { 0x0288, "tretroflexhook" },
                                                                { 0x25bc, "triagdn" },
                                                                { 0x25c4, "triaglf" },
                                                                { 0x25ba, "triagrt" },
                                                                { 0x25b2, "triagup" },
                                                                { 0x02a6, "ts" },
                                                                { 0x05e6, "tsadi" },
                                                                { 0xfb46, "tsadidagesh" },
                                                                { 0xfb46, "tsadidageshhebrew" },
                                                                { 0x05e6, "tsadihebrew" },
                                                                { 0x0446, "tsecyrillic" },
                                                                { 0x05b5, "tsere" },
                                                                { 0x05b5, "tsere12" },
                                                                { 0x05b5, "tsere1e" },
                                                                { 0x05b5, "tsere2b" },
                                                                { 0x05b5, "tserehebrew" },
                                                                { 0x05b5, "tserenarrowhebrew" },
                                                                { 0x05b5, "tserequarterhebrew" },
                                                                { 0x05b5, "tserewidehebrew" },
                                                                { 0x045b, "tshecyrillic" },
                                                                { 0xf6f3, "tsuperior" },
                                                                { 0x099f, "ttabengali" },
                                                                { 0x091f, "ttadeva" },
                                                                { 0x0a9f, "ttagujarati" },
                                                                { 0x0a1f, "ttagurmukhi" },
                                                                { 0x0679, "tteharabic" },
                                                                { 0xfb67, "ttehfinalarabic" },
                                                                { 0xfb68, "ttehinitialarabic" },
                                                                { 0xfb69, "ttehmedialarabic" },
                                                                { 0x09a0, "tthabengali" },
                                                                { 0x0920, "tthadeva" },
                                                                { 0x0aa0, "tthagujarati" },
                                                                { 0x0a20, "tthagurmukhi" },
                                                                { 0x0287, "tturned" },
                                                                { 0x3064, "tuhiragana" },
                                                                { 0x30c4, "tukatakana" },
                                                                { 0xff82, "tukatakanahalfwidth" },
                                                                { 0x3063, "tusmallhiragana" },
                                                                { 0x30c3, "tusmallkatakana" },
                                                                { 0xff6f, "tusmallkatakanahalfwidth" },
                                                                { 0x246b, "twelvecircle" },
                                                                { 0x247f, "twelveparen" },
                                                                { 0x2493, "twelveperiod" },
                                                                { 0x217b, "twelveroman" },
                                                                { 0x2473, "twentycircle" },
                                                                { 0x5344, "twentyhangzhou" },
                                                                { 0x2487, "twentyparen" },
                                                                { 0x249b, "twentyperiod" },
                                                                { 0x0032, "two" },
                                                                { 0x0662, "twoarabic" },
                                                                { 0x09e8, "twobengali" },
                                                                { 0x2461, "twocircle" },
                                                                { 0x278b, "twocircleinversesansserif" },
                                                                { 0x0968, "twodeva" },
                                                                { 0x2025, "twodotenleader" },
                                                                { 0x2025, "twodotleader" },
                                                                { 0xfe30, "twodotleadervertical" },
                                                                { 0x0ae8, "twogujarati" },
                                                                { 0x0a68, "twogurmukhi" },
                                                                { 0x0662, "twohackarabic" },
                                                                { 0x3022, "twohangzhou" },
                                                                { 0x3221, "twoideographicparen" },
                                                                { 0x2082, "twoinferior" },
                                                                { 0xff12, "twomonospace" },
                                                                { 0x09f5, "twonumeratorbengali" },
                                                                { 0xf732, "twooldstyle" },
                                                                { 0x2475, "twoparen" },
                                                                { 0x2489, "twoperiod" },
                                                                { 0x06f2, "twopersian" },
                                                                { 0x2171, "tworoman" },
                                                                { 0x01bb, "twostroke" },
                                                                { 0x00b2, "twosuperior" },
                                                                { 0x0e52, "twothai" },
                                                                { 0x2154, "twothirds" },
                                                                { 0x0075, "u" },
                                                                { 0x00fa, "uacute" },
                                                                { 0x0289, "ubar" },
                                                                { 0x0989, "ubengali" },
                                                                { 0x3128, "ubopomofo" },
                                                                { 0x016d, "ubreve" },
                                                                { 0x01d4, "ucaron" },
                                                                { 0x24e4, "ucircle" },
                                                                { 0x00fb, "ucircumflex" },
                                                                { 0x1e77, "ucircumflexbelow" },
                                                                { 0x0443, "ucyrillic" },
                                                                { 0x0951, "udattadeva" },
                                                                { 0x0171, "udblacute" },
                                                                { 0x0215, "udblgrave" },
                                                                { 0x0909, "udeva" },
                                                                { 0x00fc, "udieresis" },
                                                                { 0x01d8, "udieresisacute" },
                                                                { 0x1e73, "udieresisbelow" },
                                                                { 0x01da, "udieresiscaron" },
                                                                { 0x04f1, "udieresiscyrillic" },
                                                                { 0x01dc, "udieresisgrave" },
                                                                { 0x01d6, "udieresismacron" },
                                                                { 0x1ee5, "udotbelow" },
                                                                { 0x00f9, "ugrave" },
                                                                { 0x0a89, "ugujarati" },
                                                                { 0x0a09, "ugurmukhi" },
                                                                { 0x3046, "uhiragana" },
                                                                { 0x1ee7, "uhookabove" },
                                                                { 0x01b0, "uhorn" },
                                                                { 0x1ee9, "uhornacute" },
                                                                { 0x1ef1, "uhorndotbelow" },
                                                                { 0x1eeb, "uhorngrave" },
                                                                { 0x1eed, "uhornhookabove" },
                                                                { 0x1eef, "uhorntilde" },
                                                                { 0x0171, "uhungarumlaut" },
                                                                { 0x04f3, "uhungarumlautcyrillic" },
                                                                { 0x0217, "uinvertedbreve" },
                                                                { 0x30a6, "ukatakana" },
                                                                { 0xff73, "ukatakanahalfwidth" },
                                                                { 0x0479, "ukcyrillic" },
                                                                { 0x315c, "ukorean" },
                                                                { 0x016b, "umacron" },
                                                                { 0x04ef, "umacroncyrillic" },
                                                                { 0x1e7b, "umacrondieresis" },
                                                                { 0x0a41, "umatragurmukhi" },
                                                                { 0xff55, "umonospace" },
                                                                { 0x005f, "underscore" },
                                                                { 0x2017, "underscoredbl" },
                                                                { 0xff3f, "underscoremonospace" },
                                                                { 0xfe33, "underscorevertical" },
                                                                { 0xfe4f, "underscorewavy" },
                                                                { 0x222a, "union" },
                                                                { 0x2200, "universal" },
                                                                { 0x0173, "uogonek" },
                                                                { 0x24b0, "uparen" },
                                                                { 0x2580, "upblock" },
                                                                { 0x05c4, "upperdothebrew" },
                                                                { 0x03c5, "upsilon" },
                                                                { 0x03cb, "upsilondieresis" },
                                                                { 0x03b0, "upsilondieresistonos" },
                                                                { 0x028a, "upsilonlatin" },
                                                                { 0x03cd, "upsilontonos" },
                                                                { 0x031d, "uptackbelowcmb" },
                                                                { 0x02d4, "uptackmod" },
                                                                { 0x0a73, "uragurmukhi" },
                                                                { 0x016f, "uring" },
                                                                { 0x045e, "ushortcyrillic" },
                                                                { 0x3045, "usmallhiragana" },
                                                                { 0x30a5, "usmallkatakana" },
                                                                { 0xff69, "usmallkatakanahalfwidth" },
                                                                { 0x04af, "ustraightcyrillic" },
                                                                { 0x04b1, "ustraightstrokecyrillic" },
                                                                { 0x0169, "utilde" },
                                                                { 0x1e79, "utildeacute" },
                                                                { 0x1e75, "utildebelow" },
                                                                { 0x098a, "uubengali" },
                                                                { 0x090a, "uudeva" },
                                                                { 0x0a8a, "uugujarati" },
                                                                { 0x0a0a, "uugurmukhi" },
                                                                { 0x0a42, "uumatragurmukhi" },
                                                                { 0x09c2, "uuvowelsignbengali" },
                                                                { 0x0942, "uuvowelsigndeva" },
                                                                { 0x0ac2, "uuvowelsigngujarati" },
                                                                { 0x09c1, "uvowelsignbengali" },
                                                                { 0x0941, "uvowelsigndeva" },
                                                                { 0x0ac1, "uvowelsigngujarati" },
                                                                { 0x0076, "v" },
                                                                { 0x0935, "vadeva" },
                                                                { 0x0ab5, "vagujarati" },
                                                                { 0x0a35, "vagurmukhi" },
                                                                { 0x30f7, "vakatakana" },
                                                                { 0x05d5, "vav" },
                                                                { 0xfb35, "vavdagesh" },
                                                                { 0xfb35, "vavdagesh65" },
                                                                { 0xfb35, "vavdageshhebrew" },
                                                                { 0x05d5, "vavhebrew" },
                                                                { 0xfb4b, "vavholam" },
                                                                { 0xfb4b, "vavholamhebrew" },
                                                                { 0x05f0, "vavvavhebrew" },
                                                                { 0x05f1, "vavyodhebrew" },
                                                                { 0x24e5, "vcircle" },
                                                                { 0x1e7f, "vdotbelow" },
                                                                { 0x0432, "vecyrillic" },
                                                                { 0x06a4, "veharabic" },
                                                                { 0xfb6b, "vehfinalarabic" },
                                                                { 0xfb6c, "vehinitialarabic" },
                                                                { 0xfb6d, "vehmedialarabic" },
                                                                { 0x30f9, "vekatakana" },
                                                                { 0x2640, "venus" },
                                                                { 0x007c, "verticalbar" },
                                                                { 0x030d, "verticallineabovecmb" },
                                                                { 0x0329, "verticallinebelowcmb" },
                                                                { 0x02cc, "verticallinelowmod" },
                                                                { 0x02c8, "verticallinemod" },
                                                                { 0x057e, "vewarmenian" },
                                                                { 0x028b, "vhook" },
                                                                { 0x30f8, "vikatakana" },
                                                                { 0x09cd, "viramabengali" },
                                                                { 0x094d, "viramadeva" },
                                                                { 0x0acd, "viramagujarati" },
                                                                { 0x0983, "visargabengali" },
                                                                { 0x0903, "visargadeva" },
                                                                { 0x0a83, "visargagujarati" },
                                                                { 0xff56, "vmonospace" },
                                                                { 0x0578, "voarmenian" },
                                                                { 0x309e, "voicediterationhiragana" },
                                                                { 0x30fe, "voicediterationkatakana" },
                                                                { 0x309b, "voicedmarkkana" },
                                                                { 0xff9e, "voicedmarkkanahalfwidth" },
                                                                { 0x30fa, "vokatakana" },
                                                                { 0x24b1, "vparen" },
                                                                { 0x1e7d, "vtilde" },
                                                                { 0x028c, "vturned" },
                                                                { 0x3094, "vuhiragana" },
                                                                { 0x30f4, "vukatakana" },
                                                                { 0x0077, "w" },
                                                                { 0x1e83, "wacute" },
                                                                { 0x3159, "waekorean" },
                                                                { 0x308f, "wahiragana" },
                                                                { 0x30ef, "wakatakana" },
                                                                { 0xff9c, "wakatakanahalfwidth" },
                                                                { 0x3158, "wakorean" },
                                                                { 0x308e, "wasmallhiragana" },
                                                                { 0x30ee, "wasmallkatakana" },
                                                                { 0x3357, "wattosquare" },
                                                                { 0x301c, "wavedash" },
                                                                { 0xfe34, "wavyunderscorevertical" },
                                                                { 0x0648, "wawarabic" },
                                                                { 0xfeee, "wawfinalarabic" },
                                                                { 0x0624, "wawhamzaabovearabic" },
                                                                { 0xfe86, "wawhamzaabovefinalarabic" },
                                                                { 0x33dd, "wbsquare" },
                                                                { 0x24e6, "wcircle" },
                                                                { 0x0175, "wcircumflex" },
                                                                { 0x1e85, "wdieresis" },
                                                                { 0x1e87, "wdotaccent" },
                                                                { 0x1e89, "wdotbelow" },
                                                                { 0x3091, "wehiragana" },
                                                                { 0x2118, "weierstrass" },
                                                                { 0x30f1, "wekatakana" },
                                                                { 0x315e, "wekorean" },
                                                                { 0x315d, "weokorean" },
                                                                { 0x1e81, "wgrave" },
                                                                { 0x25e6, "whitebullet" },
                                                                { 0x25cb, "whitecircle" },
                                                                { 0x25d9, "whitecircleinverse" },
                                                                { 0x300e, "whitecornerbracketleft" },
                                                                { 0xfe43, "whitecornerbracketleftvertical" },
                                                                { 0x300f, "whitecornerbracketright" },
                                                                { 0xfe44, "whitecornerbracketrightvertical" },
                                                                { 0x25c7, "whitediamond" },
                                                                { 0x25c8, "whitediamondcontainingblacksmalldiamond" },
                                                                { 0x25bf, "whitedownpointingsmalltriangle" },
                                                                { 0x25bd, "whitedownpointingtriangle" },
                                                                { 0x25c3, "whiteleftpointingsmalltriangle" },
                                                                { 0x25c1, "whiteleftpointingtriangle" },
                                                                { 0x3016, "whitelenticularbracketleft" },
                                                                { 0x3017, "whitelenticularbracketright" },
                                                                { 0x25b9, "whiterightpointingsmalltriangle" },
                                                                { 0x25b7, "whiterightpointingtriangle" },
                                                                { 0x25ab, "whitesmallsquare" },
                                                                { 0x263a, "whitesmilingface" },
                                                                { 0x25a1, "whitesquare" },
                                                                { 0x2606, "whitestar" },
                                                                { 0x260f, "whitetelephone" },
                                                                { 0x3018, "whitetortoiseshellbracketleft" },
                                                                { 0x3019, "whitetortoiseshellbracketright" },
                                                                { 0x25b5, "whiteuppointingsmalltriangle" },
                                                                { 0x25b3, "whiteuppointingtriangle" },
                                                                { 0x3090, "wihiragana" },
                                                                { 0x30f0, "wikatakana" },
                                                                { 0x315f, "wikorean" },
                                                                { 0xff57, "wmonospace" },
                                                                { 0x3092, "wohiragana" },
                                                                { 0x30f2, "wokatakana" },
                                                                { 0xff66, "wokatakanahalfwidth" },
                                                                { 0x20a9, "won" },
                                                                { 0xffe6, "wonmonospace" },
                                                                { 0x0e27, "wowaenthai" },
                                                                { 0x24b2, "wparen" },
                                                                { 0x1e98, "wring" },
                                                                { 0x02b7, "wsuperior" },
                                                                { 0x028d, "wturned" },
                                                                { 0x01bf, "wynn" },
                                                                { 0x0078, "x" },
                                                                { 0x033d, "xabovecmb" },
                                                                { 0x3112, "xbopomofo" },
                                                                { 0x24e7, "xcircle" },
                                                                { 0x1e8d, "xdieresis" },
                                                                { 0x1e8b, "xdotaccent" },
                                                                { 0x056d, "xeharmenian" },
                                                                { 0x03be, "xi" },
                                                                { 0xff58, "xmonospace" },
                                                                { 0x24b3, "xparen" },
                                                                { 0x02e3, "xsuperior" },
                                                                { 0x0079, "y" },
                                                                { 0x334e, "yaadosquare" },
                                                                { 0x09af, "yabengali" },
                                                                { 0x00fd, "yacute" },
                                                                { 0x092f, "yadeva" },
                                                                { 0x3152, "yaekorean" },
                                                                { 0x0aaf, "yagujarati" },
                                                                { 0x0a2f, "yagurmukhi" },
                                                                { 0x3084, "yahiragana" },
                                                                { 0x30e4, "yakatakana" },
                                                                { 0xff94, "yakatakanahalfwidth" },
                                                                { 0x3151, "yakorean" },
                                                                { 0x0e4e, "yamakkanthai" },
                                                                { 0x3083, "yasmallhiragana" },
                                                                { 0x30e3, "yasmallkatakana" },
                                                                { 0xff6c, "yasmallkatakanahalfwidth" },
                                                                { 0x0463, "yatcyrillic" },
                                                                { 0x24e8, "ycircle" },
                                                                { 0x0177, "ycircumflex" },
                                                                { 0x00ff, "ydieresis" },
                                                                { 0x1e8f, "ydotaccent" },
                                                                { 0x1ef5, "ydotbelow" },
                                                                { 0x064a, "yeharabic" },
                                                                { 0x06d2, "yehbarreearabic" },
                                                                { 0xfbaf, "yehbarreefinalarabic" },
                                                                { 0xfef2, "yehfinalarabic" },
                                                                { 0x0626, "yehhamzaabovearabic" },
                                                                { 0xfe8a, "yehhamzaabovefinalarabic" },
                                                                { 0xfe8b, "yehhamzaaboveinitialarabic" },
                                                                { 0xfe8c, "yehhamzaabovemedialarabic" },
                                                                { 0xfef3, "yehinitialarabic" },
                                                                { 0xfef4, "yehmedialarabic" },
                                                                { 0xfcdd, "yehmeeminitialarabic" },
                                                                { 0xfc58, "yehmeemisolatedarabic" },
                                                                { 0xfc94, "yehnoonfinalarabic" },
                                                                { 0x06d1, "yehthreedotsbelowarabic" },
                                                                { 0x3156, "yekorean" },
                                                                { 0x00a5, "yen" },
                                                                { 0xffe5, "yenmonospace" },
                                                                { 0x3155, "yeokorean" },
                                                                { 0x3186, "yeorinhieuhkorean" },
                                                                { 0x05aa, "yerahbenyomohebrew" },
                                                                { 0x05aa, "yerahbenyomolefthebrew" },
                                                                { 0x044b, "yericyrillic" },
                                                                { 0x04f9, "yerudieresiscyrillic" },
                                                                { 0x3181, "yesieungkorean" },
                                                                { 0x3183, "yesieungpansioskorean" },
                                                                { 0x3182, "yesieungsioskorean" },
                                                                { 0x059a, "yetivhebrew" },
                                                                { 0x1ef3, "ygrave" },
                                                                { 0x01b4, "yhook" },
                                                                { 0x1ef7, "yhookabove" },
                                                                { 0x0575, "yiarmenian" },
                                                                { 0x0457, "yicyrillic" },
                                                                { 0x3162, "yikorean" },
                                                                { 0x262f, "yinyang" },
                                                                { 0x0582, "yiwnarmenian" },
                                                                { 0xff59, "ymonospace" },
                                                                { 0x05d9, "yod" },
                                                                { 0xfb39, "yoddagesh" },
                                                                { 0xfb39, "yoddageshhebrew" },
                                                                { 0x05d9, "yodhebrew" },
                                                                { 0x05f2, "yodyodhebrew" },
                                                                { 0xfb1f, "yodyodpatahhebrew" },
                                                                { 0x3088, "yohiragana" },
                                                                { 0x3189, "yoikorean" },
                                                                { 0x30e8, "yokatakana" },
                                                                { 0xff96, "yokatakanahalfwidth" },
                                                                { 0x315b, "yokorean" },
                                                                { 0x3087, "yosmallhiragana" },
                                                                { 0x30e7, "yosmallkatakana" },
                                                                { 0xff6e, "yosmallkatakanahalfwidth" },
                                                                { 0x03f3, "yotgreek" },
                                                                { 0x3188, "yoyaekorean" },
                                                                { 0x3187, "yoyakorean" },
                                                                { 0x0e22, "yoyakthai" },
                                                                { 0x0e0d, "yoyingthai" },
                                                                { 0x24b4, "yparen" },
                                                                { 0x037a, "ypogegrammeni" },
                                                                { 0x0345, "ypogegrammenigreekcmb" },
                                                                { 0x01a6, "yr" },
                                                                { 0x1e99, "yring" },
                                                                { 0x02b8, "ysuperior" },
                                                                { 0x1ef9, "ytilde" },
                                                                { 0x028e, "yturned" },
                                                                { 0x3086, "yuhiragana" },
                                                                { 0x318c, "yuikorean" },
                                                                { 0x30e6, "yukatakana" },
                                                                { 0xff95, "yukatakanahalfwidth" },
                                                                { 0x3160, "yukorean" },
                                                                { 0x046b, "yusbigcyrillic" },
                                                                { 0x046d, "yusbigiotifiedcyrillic" },
                                                                { 0x0467, "yuslittlecyrillic" },
                                                                { 0x0469, "yuslittleiotifiedcyrillic" },
                                                                { 0x3085, "yusmallhiragana" },
                                                                { 0x30e5, "yusmallkatakana" },
                                                                { 0xff6d, "yusmallkatakanahalfwidth" },
                                                                { 0x318b, "yuyekorean" },
                                                                { 0x318a, "yuyeokorean" },
                                                                { 0x09df, "yyabengali" },
                                                                { 0x095f, "yyadeva" },
                                                                { 0x007a, "z" },
                                                                { 0x0566, "zaarmenian" },
                                                                { 0x017a, "zacute" },
                                                                { 0x095b, "zadeva" },
                                                                { 0x0a5b, "zagurmukhi" },
                                                                { 0x0638, "zaharabic" },
                                                                { 0xfec6, "zahfinalarabic" },
                                                                { 0xfec7, "zahinitialarabic" },
                                                                { 0x3056, "zahiragana" },
                                                                { 0xfec8, "zahmedialarabic" },
                                                                { 0x0632, "zainarabic" },
                                                                { 0xfeb0, "zainfinalarabic" },
                                                                { 0x30b6, "zakatakana" },
                                                                { 0x0595, "zaqefgadolhebrew" },
                                                                { 0x0594, "zaqefqatanhebrew" },
                                                                { 0x0598, "zarqahebrew" },
                                                                { 0x05d6, "zayin" },
                                                                { 0xfb36, "zayindagesh" },
                                                                { 0xfb36, "zayindageshhebrew" },
                                                                { 0x05d6, "zayinhebrew" },
                                                                { 0x3117, "zbopomofo" },
                                                                { 0x017e, "zcaron" },
                                                                { 0x24e9, "zcircle" },
                                                                { 0x1e91, "zcircumflex" },
                                                                { 0x0291, "zcurl" },
                                                                { 0x017c, "zdot" },
                                                                { 0x017c, "zdotaccent" },
                                                                { 0x1e93, "zdotbelow" },
                                                                { 0x0437, "zecyrillic" },
                                                                { 0x0499, "zedescendercyrillic" },
                                                                { 0x04df, "zedieresiscyrillic" },
                                                                { 0x305c, "zehiragana" },
                                                                { 0x30bc, "zekatakana" },
                                                                { 0x0030, "zero" },
                                                                { 0x0660, "zeroarabic" },
                                                                { 0x09e6, "zerobengali" },
                                                                { 0x0966, "zerodeva" },
                                                                { 0x0ae6, "zerogujarati" },
                                                                { 0x0a66, "zerogurmukhi" },
                                                                { 0x0660, "zerohackarabic" },
                                                                { 0x2080, "zeroinferior" },
                                                                { 0xff10, "zeromonospace" },
                                                                { 0xf730, "zerooldstyle" },
                                                                { 0x06f0, "zeropersian" },
                                                                { 0x2070, "zerosuperior" },
                                                                { 0x0e50, "zerothai" },
                                                                { 0xfeff, "zerowidthjoiner" },
                                                                { 0x200c, "zerowidthnonjoiner" },
                                                                { 0x200b, "zerowidthspace" },
                                                                { 0x03b6, "zeta" },
                                                                { 0x3113, "zhbopomofo" },
                                                                { 0x056a, "zhearmenian" },
                                                                { 0x04c2, "zhebrevecyrillic" },
                                                                { 0x0436, "zhecyrillic" },
                                                                { 0x0497, "zhedescendercyrillic" },
                                                                { 0x04dd, "zhedieresiscyrillic" },
                                                                { 0x3058, "zihiragana" },
                                                                { 0x30b8, "zikatakana" },
                                                                { 0x05ae, "zinorhebrew" },
                                                                { 0x1e95, "zlinebelow" },
                                                                { 0xff5a, "zmonospace" },
                                                                { 0x305e, "zohiragana" },
                                                                { 0x30be, "zokatakana" },
                                                                { 0x24b5, "zparen" },
                                                                { 0x0290, "zretroflexhook" },
                                                                { 0x01b6, "zstroke" },
                                                                { 0x305a, "zuhiragana" },
                                                                { 0x30ba, "zukatakana" },
                                                                { 0x007b, "{" },
                                                                { 0x007c, "|" },
                                                                { 0x007d, "}" },
                                                                { 0x007e, "~" },
                                                                { 0, nullptr } };

// map ZapfDingbats names to Unicode
static const struct NameToUnicodeTab nameToUnicodeZapfDingbatsTab[] = {
    { 0x275e, "a100" }, { 0x2761, "a101" }, { 0x2762, "a102" }, { 0x2763, "a103" }, { 0x2764, "a104" }, { 0x2710, "a105" }, { 0x2765, "a106" }, { 0x2766, "a107" }, { 0x2767, "a108" }, { 0x2660, "a109" }, { 0x2721, "a10" },
    { 0x2665, "a110" }, { 0x2666, "a111" }, { 0x2663, "a112" }, { 0x2709, "a117" }, { 0x2708, "a118" }, { 0x2707, "a119" }, { 0x261b, "a11" },  { 0x2460, "a120" }, { 0x2461, "a121" }, { 0x2462, "a122" }, { 0x2463, "a123" },
    { 0x2464, "a124" }, { 0x2465, "a125" }, { 0x2466, "a126" }, { 0x2467, "a127" }, { 0x2468, "a128" }, { 0x2469, "a129" }, { 0x261e, "a12" },  { 0x2776, "a130" }, { 0x2777, "a131" }, { 0x2778, "a132" }, { 0x2779, "a133" },
    { 0x277a, "a134" }, { 0x277b, "a135" }, { 0x277c, "a136" }, { 0x277d, "a137" }, { 0x277e, "a138" }, { 0x277f, "a139" }, { 0x270c, "a13" },  { 0x2780, "a140" }, { 0x2781, "a141" }, { 0x2782, "a142" }, { 0x2783, "a143" },
    { 0x2784, "a144" }, { 0x2785, "a145" }, { 0x2786, "a146" }, { 0x2787, "a147" }, { 0x2788, "a148" }, { 0x2789, "a149" }, { 0x270d, "a14" },  { 0x278a, "a150" }, { 0x278b, "a151" }, { 0x278c, "a152" }, { 0x278d, "a153" },
    { 0x278e, "a154" }, { 0x278f, "a155" }, { 0x2790, "a156" }, { 0x2791, "a157" }, { 0x2792, "a158" }, { 0x2793, "a159" }, { 0x270e, "a15" },  { 0x2794, "a160" }, { 0x2192, "a161" }, { 0x27a3, "a162" }, { 0x2194, "a163" },
    { 0x2195, "a164" }, { 0x2799, "a165" }, { 0x279b, "a166" }, { 0x279c, "a167" }, { 0x279d, "a168" }, { 0x279e, "a169" }, { 0x270f, "a16" },  { 0x279f, "a170" }, { 0x27a0, "a171" }, { 0x27a1, "a172" }, { 0x27a2, "a173" },
    { 0x27a4, "a174" }, { 0x27a5, "a175" }, { 0x27a6, "a176" }, { 0x27a7, "a177" }, { 0x27a8, "a178" }, { 0x27a9, "a179" }, { 0x2711, "a17" },  { 0x27ab, "a180" }, { 0x27ad, "a181" }, { 0x27af, "a182" }, { 0x27b2, "a183" },
    { 0x27b3, "a184" }, { 0x27b5, "a185" }, { 0x27b8, "a186" }, { 0x27ba, "a187" }, { 0x27bb, "a188" }, { 0x27bc, "a189" }, { 0x2712, "a18" },  { 0x27bd, "a190" }, { 0x27be, "a191" }, { 0x279a, "a192" }, { 0x27aa, "a193" },
    { 0x27b6, "a194" }, { 0x27b9, "a195" }, { 0x2798, "a196" }, { 0x27b4, "a197" }, { 0x27b7, "a198" }, { 0x27ac, "a199" }, { 0x2713, "a19" },  { 0x2701, "a1" },   { 0x27ae, "a200" }, { 0x27b1, "a201" }, { 0x2703, "a202" },
    { 0x2750, "a203" }, { 0x2752, "a204" }, { 0x276e, "a205" }, { 0x2770, "a206" }, { 0x2714, "a20" },  { 0x2715, "a21" },  { 0x2716, "a22" },  { 0x2717, "a23" },  { 0x2718, "a24" },  { 0x2719, "a25" },  { 0x271a, "a26" },
    { 0x271b, "a27" },  { 0x271c, "a28" },  { 0x2722, "a29" },  { 0x2702, "a2" },   { 0x2723, "a30" },  { 0x2724, "a31" },  { 0x2725, "a32" },  { 0x2726, "a33" },  { 0x2727, "a34" },  { 0x2605, "a35" },  { 0x2729, "a36" },
    { 0x272a, "a37" },  { 0x272b, "a38" },  { 0x272c, "a39" },  { 0x2704, "a3" },   { 0x272d, "a40" },  { 0x272e, "a41" },  { 0x272f, "a42" },  { 0x2730, "a43" },  { 0x2731, "a44" },  { 0x2732, "a45" },  { 0x2733, "a46" },
    { 0x2734, "a47" },  { 0x2735, "a48" },  { 0x2736, "a49" },  { 0x260e, "a4" },   { 0x2737, "a50" },  { 0x2738, "a51" },  { 0x2739, "a52" },  { 0x273a, "a53" },  { 0x273b, "a54" },  { 0x273c, "a55" },  { 0x273d, "a56" },
    { 0x273e, "a57" },  { 0x273f, "a58" },  { 0x2740, "a59" },  { 0x2706, "a5" },   { 0x2741, "a60" },  { 0x2742, "a61" },  { 0x2743, "a62" },  { 0x2744, "a63" },  { 0x2745, "a64" },  { 0x2746, "a65" },  { 0x2747, "a66" },
    { 0x2748, "a67" },  { 0x2749, "a68" },  { 0x274a, "a69" },  { 0x271d, "a6" },   { 0x274b, "a70" },  { 0x25cf, "a71" },  { 0x274d, "a72" },  { 0x25a0, "a73" },  { 0x274f, "a74" },  { 0x2751, "a75" },  { 0x25b2, "a76" },
    { 0x25bc, "a77" },  { 0x25c6, "a78" },  { 0x2756, "a79" },  { 0x271e, "a7" },   { 0x25d7, "a81" },  { 0x2758, "a82" },  { 0x2759, "a83" },  { 0x275a, "a84" },  { 0x276f, "a85" },  { 0x2771, "a86" },  { 0x2772, "a87" },
    { 0x2773, "a88" },  { 0x2768, "a89" },  { 0x271f, "a8" },   { 0x2769, "a90" },  { 0x276c, "a91" },  { 0x276d, "a92" },  { 0x276a, "a93" },  { 0x276b, "a94" },  { 0x2774, "a95" },  { 0x2775, "a96" },  { 0x275b, "a97" },
    { 0x275c, "a98" },  { 0x275d, "a99" },  { 0x2720, "a9" },   { 0, nullptr }
};
