#!/usr/bin/env python3
"""
Script de instalación completa para el sistema de extracción de QR de constancias.
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
import shutil
from pathlib import Path

def ejecutar_comando(comando, descripcion):
    """Ejecuta un comando y maneja errores."""
    print(f"Ejecutando: {descripcion}")
    try:
        result = subprocess.run(comando, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {descripcion} completado")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error en {descripcion}: {e}")
        print(f"Salida del error: {e.stderr}")
        return False

def instalar_dependencias():
    """Instala las dependencias de Python necesarias."""
    print("\n=== INSTALANDO DEPENDENCIAS DE PYTHON ===")
    
    dependencias = [
        "pdf2image",
        "opencv-python", 
        "Pillow",
        "numpy"
    ]
    
    for dep in dependencias:
        if not ejecutar_comando(f"pip install {dep}", f"Instalando {dep}"):
            return False
    
    return True

def descargar_poppler():
    """Descarga e instala Poppler para Windows."""
    print("\n=== INSTALANDO POPPLER ===")
    
    poppler_local = Path("poppler")
    if poppler_local.exists():
        print("✓ Poppler ya está instalado")
        return True
    
    print("Descargando Poppler para Windows...")
    poppler_url = "https://github.com/oschwartz10612/poppler-windows/releases/download/v24.08.0-0/Release-24.08.0-0.zip"
    
    temp_dir = Path("temp_poppler")
    temp_dir.mkdir(exist_ok=True)
    zip_path = temp_dir / "poppler.zip"
    
    try:
        urllib.request.urlretrieve(poppler_url, zip_path)
        print("✓ Descarga completada")
        
        print("Extrayendo archivos...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        
        extracted_folders = [f for f in temp_dir.iterdir() if f.is_dir() and f.name.startswith("poppler")]
        
        if not extracted_folders:
            print("✗ Error: No se encontró la carpeta de Poppler extraída")
            return False
            
        poppler_folder = extracted_folders[0]
        shutil.move(str(poppler_folder), str(poppler_local))
        shutil.rmtree(temp_dir)
        
        print(f"✓ Poppler instalado en: {poppler_local.absolute()}")
        return True
        
    except Exception as e:
        print(f"✗ Error instalando Poppler: {e}")
        return False

def verificar_instalacion():
    """Verifica que todo esté instalado correctamente."""
    print("\n=== VERIFICANDO INSTALACIÓN ===")
    
    # Verificar dependencias de Python
    dependencias = ["pdf2image", "cv2", "PIL", "numpy"]
    
    for dep in dependencias:
        try:
            __import__(dep)
            print(f"✓ {dep} instalado correctamente")
        except ImportError:
            print(f"✗ {dep} no está disponible")
            return False
    
    # Verificar Poppler
    poppler_path = Path("poppler/Library/bin")
    if poppler_path.exists():
        print("✓ Poppler instalado correctamente")
    else:
        print("✗ Poppler no encontrado")
        return False
    
    return True

def crear_script_ejecutor():
    """Crea un script batch para ejecutar fácilmente."""
    print("\n=== CREANDO SCRIPT EJECUTOR ===")
    
    script_content = """@echo off
echo ================================================
echo    EXTRACTOR DE QR DE CONSTANCIAS SAT
echo ================================================
echo.

echo Ejecutando extraccion de codigos QR...
python extraer_qr_opencv.py

echo.
echo Analizando informacion extraida...
python analizar_qr_constancias.py

echo.
echo ================================================
echo Proceso completado. Revisa los archivos:
echo - qr_extraidos_opencv.txt
echo - reporte_qr_legible.txt  
echo - reporte_qr_resumen.csv
echo ================================================
pause
"""
    
    with open("ejecutar_extraccion.bat", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("✓ Script ejecutor creado: ejecutar_extraccion.bat")

def mostrar_instrucciones():
    """Muestra las instrucciones finales de uso."""
    print("\n" + "="*60)
    print("           INSTALACIÓN COMPLETADA")
    print("="*60)
    print()
    print("ARCHIVOS DISPONIBLES:")
    print("- extraer_qr_opencv.py      : Script principal de extracción")
    print("- analizar_qr_constancias.py: Analizador de información")
    print("- ejecutar_extraccion.bat   : Script para ejecutar todo")
    print()
    print("PARA USAR EL SISTEMA:")
    print("1. Coloca tus archivos PDF en la carpeta 'Constancias'")
    print("2. Ejecuta: python extraer_qr_opencv.py")
    print("3. Ejecuta: python analizar_qr_constancias.py")
    print("   O simplemente haz doble clic en: ejecutar_extraccion.bat")
    print()
    print("ARCHIVOS DE SALIDA:")
    print("- qr_extraidos_opencv.txt   : QR extraídos en texto plano")
    print("- reporte_qr_legible.txt    : Reporte detallado legible")
    print("- reporte_qr_resumen.csv    : Datos para Excel")
    print("- reporte_qr_detallado.json : Datos estructurados JSON")
    print()
    print("="*60)

def main():
    """Función principal de instalación."""
    print("="*60)
    print("    INSTALADOR DEL SISTEMA DE EXTRACCIÓN DE QR")
    print("="*60)
    
    # Verificar que estamos en Windows
    if os.name != 'nt':
        print("Este instalador está diseñado para Windows.")
        print("En otros sistemas, instala manualmente las dependencias.")
        return
    
    # Instalar dependencias
    if not instalar_dependencias():
        print("✗ Error instalando dependencias de Python")
        return
    
    # Instalar Poppler
    if not descargar_poppler():
        print("✗ Error instalando Poppler")
        return
    
    # Verificar instalación
    if not verificar_instalacion():
        print("✗ La verificación de instalación falló")
        return
    
    # Crear script ejecutor
    crear_script_ejecutor()
    
    # Mostrar instrucciones
    mostrar_instrucciones()
    
    print("\n¡Instalación completada exitosamente!")

if __name__ == "__main__":
    main()
