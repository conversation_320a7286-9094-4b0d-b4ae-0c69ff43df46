#!/usr/bin/env python3
"""
Script para analizar y extraer información estructurada de los códigos QR de constancias SAT.
"""

import os
import re
import json
import csv
from urllib.parse import urlparse, parse_qs
from datetime import datetime
from pathlib import Path

def extraer_informacion_qr(url_qr):
    """
    Extrae información estructurada de una URL de QR del SAT.
    
    Args:
        url_qr: URL completa del código QR
        
    Returns:
        Diccionario con información extraída
    """
    info = {
        'url_completa': url_qr,
        'valida': False,
        'tipo_documento': None,
        'parametros': {},
        'identificador': None,
        'rfc': None,
        'fecha_posible': None
    }
    
    try:
        # Parsear la URL
        parsed_url = urlparse(url_qr)
        
        # Verificar que sea una URL del SAT
        if 'sat.gob.mx' in parsed_url.netloc:
            info['valida'] = True
            info['tipo_documento'] = 'Constancia SAT'
        
        # Extraer parámetros de la query string
        query_params = parse_qs(parsed_url.query)
        
        for key, value in query_params.items():
            if value:  # Si tiene valor
                info['parametros'][key] = value[0] if len(value) == 1 else value
        
        # Extraer información del parámetro D3 (que parece contener el identificador y RFC)
        if 'D3' in info['parametros']:
            d3_value = info['parametros']['D3']
            
            # El formato parece ser: NUMERO_RFC
            if '_' in d3_value:
                partes = d3_value.split('_')
                if len(partes) >= 2:
                    info['identificador'] = partes[0]
                    info['rfc'] = partes[1]
                    
                    # Intentar extraer fecha del identificador (primeros dígitos podrían ser fecha)
                    # Formato posible: YYMMDD... o YYYYMMDD...
                    identificador = partes[0]
                    if len(identificador) >= 6:
                        # Intentar formato YYMMDD
                        try:
                            if len(identificador) >= 8 and identificador[:2] in ['20', '21', '22', '23', '24']:
                                # Formato YYYYMMDD
                                year = int(identificador[:4])
                                month = int(identificador[4:6])
                                day = int(identificador[6:8])
                                if 1 <= month <= 12 and 1 <= day <= 31:
                                    info['fecha_posible'] = f"{year:04d}-{month:02d}-{day:02d}"
                            else:
                                # Formato YYMMDD
                                year = int(identificador[:2])
                                # Asumir que años 00-30 son 2000-2030, 31-99 son 1931-1999
                                year = 2000 + year if year <= 30 else 1900 + year
                                month = int(identificador[2:4])
                                day = int(identificador[4:6])
                                if 1 <= month <= 12 and 1 <= day <= 31:
                                    info['fecha_posible'] = f"{year:04d}-{month:02d}-{day:02d}"
                        except (ValueError, IndexError):
                            pass
        
    except Exception as e:
        info['error'] = str(e)
    
    return info

def procesar_archivo_qr(archivo_qr):
    """
    Procesa el archivo de QR extraídos y genera información estructurada.
    
    Args:
        archivo_qr: Ruta al archivo con los QR extraídos
        
    Returns:
        Lista de diccionarios con información procesada
    """
    resultados = []
    
    try:
        with open(archivo_qr, 'r', encoding='utf-8') as f:
            contenido = f.read()
        
        # Buscar patrones de archivos y URLs
        patron_archivo = r'Archivo: (.+?)\.pdf'
        patron_qr = r'QR \d+: (https?://[^\s]+)'
        
        archivos = re.findall(patron_archivo, contenido)
        urls = re.findall(patron_qr, contenido)
        
        # Procesar línea por línea para mantener la asociación archivo-QR
        lineas = contenido.split('\n')
        archivo_actual = None
        
        for linea in lineas:
            linea = linea.strip()
            
            # Detectar nuevo archivo
            match_archivo = re.search(patron_archivo, linea)
            if match_archivo:
                archivo_actual = match_archivo.group(1) + '.pdf'
                continue
            
            # Detectar QR
            match_qr = re.search(patron_qr, linea)
            if match_qr and archivo_actual:
                url_qr = match_qr.group(1)
                info_qr = extraer_informacion_qr(url_qr)
                
                resultado = {
                    'archivo': archivo_actual,
                    'timestamp_procesamiento': datetime.now().isoformat(),
                    **info_qr
                }
                
                resultados.append(resultado)
        
    except Exception as e:
        print(f"Error procesando archivo: {e}")
    
    return resultados

def generar_reportes(datos_procesados):
    """
    Genera diferentes tipos de reportes con los datos procesados.
    
    Args:
        datos_procesados: Lista de diccionarios con información de QR
    """
    
    # 1. Reporte JSON detallado
    with open('reporte_qr_detallado.json', 'w', encoding='utf-8') as f:
        json.dump(datos_procesados, f, indent=2, ensure_ascii=False)
    
    print("✓ Reporte JSON generado: reporte_qr_detallado.json")
    
    # 2. Reporte CSV para Excel
    if datos_procesados:
        campos_csv = ['archivo', 'rfc', 'identificador', 'fecha_posible', 'url_completa', 'valida']
        
        with open('reporte_qr_resumen.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=campos_csv)
            writer.writeheader()
            
            for dato in datos_procesados:
                fila = {campo: dato.get(campo, '') for campo in campos_csv}
                writer.writerow(fila)
        
        print("✓ Reporte CSV generado: reporte_qr_resumen.csv")
    
    # 3. Reporte de texto legible
    with open('reporte_qr_legible.txt', 'w', encoding='utf-8') as f:
        f.write("REPORTE DE ANÁLISIS DE CÓDIGOS QR - CONSTANCIAS SAT\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Fecha de análisis: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total de constancias procesadas: {len(datos_procesados)}\n\n")
        
        # Estadísticas
        rfcs_unicos = set(dato.get('rfc', '') for dato in datos_procesados if dato.get('rfc'))
        fechas_encontradas = sum(1 for dato in datos_procesados if dato.get('fecha_posible'))
        
        f.write("ESTADÍSTICAS:\n")
        f.write("-" * 20 + "\n")
        f.write(f"RFCs únicos encontrados: {len(rfcs_unicos)}\n")
        f.write(f"Constancias con fecha identificada: {fechas_encontradas}\n")
        f.write(f"URLs válidas del SAT: {sum(1 for dato in datos_procesados if dato.get('valida'))}\n\n")
        
        # Detalle por archivo
        f.write("DETALLE POR ARCHIVO:\n")
        f.write("-" * 30 + "\n")
        
        for dato in datos_procesados:
            f.write(f"\nArchivo: {dato.get('archivo', 'N/A')}\n")
            f.write(f"  RFC: {dato.get('rfc', 'No identificado')}\n")
            f.write(f"  Identificador: {dato.get('identificador', 'No identificado')}\n")
            f.write(f"  Fecha posible: {dato.get('fecha_posible', 'No identificada')}\n")
            f.write(f"  URL válida: {'Sí' if dato.get('valida') else 'No'}\n")
        
        # Lista de RFCs únicos
        if rfcs_unicos:
            f.write(f"\n\nRFCs ÚNICOS ENCONTRADOS:\n")
            f.write("-" * 25 + "\n")
            for rfc in sorted(rfcs_unicos):
                if rfc:  # Solo RFCs no vacíos
                    f.write(f"  {rfc}\n")
    
    print("✓ Reporte legible generado: reporte_qr_legible.txt")

def main():
    """Función principal."""
    print("=== ANALIZADOR DE CÓDIGOS QR DE CONSTANCIAS SAT ===\n")
    
    # Buscar archivo de QR extraídos
    archivos_qr = [
        'qr_extraidos_opencv.txt',
        'qr_extraidos_windows.txt',
        'qr_extraidos.txt'
    ]
    
    archivo_encontrado = None
    for archivo in archivos_qr:
        if os.path.exists(archivo):
            archivo_encontrado = archivo
            break
    
    if not archivo_encontrado:
        print("Error: No se encontró ningún archivo de QR extraídos.")
        print("Ejecuta primero uno de los scripts de extracción de QR.")
        return
    
    print(f"Procesando archivo: {archivo_encontrado}")
    
    # Procesar datos
    datos_procesados = procesar_archivo_qr(archivo_encontrado)
    
    if not datos_procesados:
        print("No se encontraron datos para procesar.")
        return
    
    print(f"Se procesaron {len(datos_procesados)} códigos QR\n")
    
    # Generar reportes
    generar_reportes(datos_procesados)
    
    # Mostrar resumen en pantalla
    print(f"\nRESUMEN DEL ANÁLISIS:")
    print("-" * 30)
    
    rfcs_unicos = set(dato.get('rfc', '') for dato in datos_procesados if dato.get('rfc'))
    fechas_encontradas = sum(1 for dato in datos_procesados if dato.get('fecha_posible'))
    
    print(f"Total de constancias: {len(datos_procesados)}")
    print(f"RFCs únicos: {len(rfcs_unicos)}")
    print(f"Fechas identificadas: {fechas_encontradas}")
    print(f"URLs válidas: {sum(1 for dato in datos_procesados if dato.get('valida'))}")
    
    if rfcs_unicos:
        print(f"\nRFCs encontrados:")
        for rfc in sorted(rfcs_unicos):
            if rfc:
                print(f"  - {rfc}")

if __name__ == "__main__":
    main()
