# Extractor de Códigos QR de Constancias SAT

Sistema completo para extraer y analizar códigos QR de constancias del SAT desde archivos PDF.

## 🚀 Instalación Rápida (Windows)

**Opción más fácil:**
```bash
python setup_completo.py
```

Este script instala automáticamente todas las dependencias y configura el sistema.

## 📁 Archivos incluidos

### Scripts principales:
- `extraer_qr_opencv.py` - **Script principal** (más confiable en Windows)
- `analizar_qr_constancias.py` - Analizador de información extraída
- `setup_completo.py` - Instalador automático
- `ejecutar_extraccion.bat` - Script para ejecutar todo fácilmente

### Scripts alternativos:
- `extraer_qr_constancias.py` - Versión avanzada con múltiples métodos
- `extraer_qr_simple.py` - Versión simplificada
- `extraer_qr_windows.py` - Versión específica para Windows
- `instalar_poppler.py` - Instalador de Poppler

### Archivos de configuración:
- `requirements.txt` - Dependencias necesarias
- `README.md` - Este archivo de documentación

## 🛠️ Instalación Manual

### Dependencias de Python:
```bash
pip install pdf2image opencv-python Pillow numpy
```

### Poppler (necesario para convertir PDF a imagen):
```bash
python instalar_poppler.py
```

## 🎯 Uso

### 🚀 Método más fácil (NUEVO - Sistema Completo):
```bash
python sistema_completo_validacion.py
```
Este script ejecuta todo el proceso automáticamente:
1. Extrae códigos QR de los PDFs
2. Valida las URLs en el sitio web del SAT
3. Genera archivos Excel con información completa

### Método paso a paso:

**Paso 1: Extraer códigos QR**
```bash
python extraer_qr_opencv.py
```

**Paso 2: Validar información en web del SAT**
```bash
python validador_sat_playwright.py
```

**Paso 3 (opcional): Análisis adicional**
```bash
python analizar_qr_constancias.py
```

### ✨ Características principales:
- **Extracción automática** de códigos QR de PDFs
- **Validación web real** accediendo al sitio del SAT
- **Información completa** de contribuyentes (nombre, dirección, régimen, etc.)
- **Múltiples formatos** de salida (Excel CSV, TXT, JSON)
- **Detección robusta** con OpenCV y Playwright
- **Manejo de SSL** y problemas de conectividad

## Estructura esperada

```
tu_proyecto/
├── Constancias/
│   ├── SAT-1.pdf
│   ├── SAT-2.pdf
│   └── ...
├── extraer_qr_constancias.py
├── extraer_qr_simple.py
└── requirements.txt
```

## 📊 Archivos de salida

El sistema genera múltiples archivos con información completa:

### 🎯 Archivos principales (para Excel):
- **`resumen_sat_playwright.csv`** - Tabla resumen para Excel con datos clave
- **`validacion_sat_playwright.csv`** - Datos completos para Excel
- `reporte_validacion_sat_playwright.txt` - Reporte detallado legible

### 📋 Información extraída del SAT:
**Datos de identificación:**
- RFC, CURP, Nombre completo
- Fecha de nacimiento, Situación del contribuyente
- Fechas de inicio de operaciones y cambios

**Datos de ubicación:**
- Estado, Municipio, Colonia, Código Postal
- Dirección completa (calle, número)
- Correo electrónico, Administración Local

**Características fiscales:**
- Régimen fiscal actual
- Fecha de alta en el régimen

## 📈 Ejemplo de salida

```
Encontrados 12 archivos PDF
Usando OpenCV para detección de QR con múltiples técnicas de procesamiento

Procesando: SAT-1.pdf
    Intentando con DPI: 300, Poppler: poppler\Library\bin
    ✓ QR detectado con: Original
  ✓ QR 1 encontrado: https://siat.sat.gob.mx/app/qr/faces/pages/mobile/validadorqr.jsf?D1=10&D2=1&D3=...

============================================================
RESUMEN DE CÓDIGOS QR EXTRAÍDOS
============================================================
Total de archivos procesados: 12
Archivos con códigos QR: 12
Total de códigos QR encontrados: 12
Tasa de éxito: 100.0%

RFCs encontrados:
  - AAOS921231UR1
  - AEMN940308PF4
  - CADP010627E94
  [...]
```

## Solución de problemas

### Error: "No module named 'pdf2image'"
```bash
pip install pdf2image
```

### Error: "Unable to get page count"
- Verifica que Poppler esté instalado correctamente
- En Windows, asegúrate de que la carpeta `bin` de Poppler esté en el PATH

### Error: "No se encontraron códigos QR"
- Verifica que los PDFs contengan códigos QR en la primera página
- Los códigos QR deben ser legibles y no estar dañados
- Prueba aumentar el DPI en el código (línea con `dpi=300`)

### Error de memoria con archivos grandes
- Reduce el DPI en `convert_from_path`
- Procesa los archivos de uno en uno en lugar de todos juntos

## Personalización

### Cambiar carpeta de origen
Modifica la variable `carpeta_constancias` en el script:
```python
carpeta_constancias = "MiCarpetaPersonalizada"
```

### Cambiar resolución de procesamiento
Modifica el parámetro `dpi` en `convert_from_path`:
```python
imagenes = convert_from_path(ruta_pdf, first_page=1, last_page=1, dpi=150)  # Menor calidad, más rápido
```

## Notas importantes

- Los scripts solo procesan la **primera página** de cada PDF
- Se requiere que los códigos QR estén en formato estándar
- Los archivos PDF deben ser legibles (no estar corruptos)
- El procesamiento puede tomar tiempo dependiendo del número y tamaño de los archivos
