#!/usr/bin/env python3
"""
Validador SAT usando Playwright para evitar problemas de SSL.
Accede a las páginas web del SAT y extrae información detallada para Excel.
"""

import os
import time
import json
import csv
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

try:
    from playwright.sync_api import sync_playwright
except ImportError as e:
    print(f"Error: Instala Playwright con:")
    print(f"pip install playwright")
    print(f"playwright install")
    print(f"Dependencia faltante: {e}")
    exit(1)


class ValidadorSATPlaywright:
    def __init__(self):
        """Inicializa el validador web del SAT con Playwright."""
        self.playwright = None
        self.browser = None
        self.page = None
        self.resultados = []
        self.delay_entre_requests = 3  # Segundos entre requests para no sobrecargar el servidor
    
    def inicializar_navegador(self):
        """Inicializa el navegador Playwright."""
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.launch(headless=True)
            self.page = self.browser.new_page()
            
            # Configurar timeouts
            self.page.set_default_timeout(30000)  # 30 segundos
            
            print("✓ Navegador inicializado correctamente")
            return True
            
        except Exception as e:
            print(f"✗ Error inicializando navegador: {e}")
            return False
    
    def cerrar_navegador(self):
        """Cierra el navegador y limpia recursos."""
        try:
            if self.page:
                self.page.close()
            if self.browser:
                self.browser.close()
            if self.playwright:
                self.playwright.stop()
            print("✓ Navegador cerrado correctamente")
        except Exception as e:
            print(f"Advertencia: Error cerrando navegador: {e}")
    
    def extraer_informacion_pagina(self, url: str) -> Dict:
        """
        Extrae información estructurada de la página del SAT usando Playwright.
        
        Args:
            url: URL del código QR del SAT
            
        Returns:
            Diccionario con información extraída
        """
        info = {
            'url_consultada': url,
            'timestamp_consulta': datetime.now().isoformat(),
            'pagina_valida': False,
            'error': None,
            
            # Datos de identificación
            'rfc': None,
            'curp': None,
            'nombre': None,
            'apellido_paterno': None,
            'apellido_materno': None,
            'nombre_completo': None,
            'fecha_nacimiento': None,
            'fecha_inicio_operaciones': None,
            'situacion_contribuyente': None,
            'fecha_ultimo_cambio_situacion': None,
            
            # Datos de ubicación
            'entidad_federativa': None,
            'municipio': None,
            'colonia': None,
            'tipo_vialidad': None,
            'nombre_vialidad': None,
            'numero_exterior': None,
            'numero_interior': None,
            'codigo_postal': None,
            'correo_electronico': None,
            'administracion_local': None,
            
            # Características fiscales
            'regimen_fiscal': None,
            'fecha_alta_regimen': None
        }
        
        try:
            # Navegar a la página
            self.page.goto(url)
            
            # Esperar a que la página cargue
            self.page.wait_for_load_state('networkidle')
            
            # Obtener el texto de la página
            page_text = self.page.inner_text('body')
            
            # Verificar que la página sea válida
            if 'El RFC:' in page_text and 'tiene asociada la siguiente información' in page_text:
                info['pagina_valida'] = True
                
                # Extraer RFC
                rfc_match = re.search(r'El RFC: ([A-Z0-9]+),', page_text)
                if rfc_match:
                    info['rfc'] = rfc_match.group(1)
            
            # Extraer información usando patrones de texto
            patterns = {
                'curp': r'CURP:\s*([A-Z0-9]+)',
                'nombre': r'Nombre:\s*([A-ZÁÉÍÓÚÑ\s]+?)(?:\n|Apellido)',
                'apellido_paterno': r'Apellido Paterno:\s*([A-ZÁÉÍÓÚÑ\s]+?)(?:\n|Apellido)',
                'apellido_materno': r'Apellido Materno:\s*([A-ZÁÉÍÓÚÑ\s]+?)(?:\n|Fecha)',
                'fecha_nacimiento': r'Fecha Nacimiento:\s*(\d{2}-\d{2}-\d{4})',
                'fecha_inicio_operaciones': r'Fecha de Inicio de operaciones:\s*(\d{2}-\d{2}-\d{4})',
                'situacion_contribuyente': r'Situación del contribuyente:\s*([A-Z]+)',
                'fecha_ultimo_cambio_situacion': r'Fecha del último cambio de situación:\s*(\d{2}-\d{2}-\d{4})',
                'entidad_federativa': r'Entidad Federativa:\s*([A-ZÁÉÍÓÚÑ\s]+?)(?:\n|Municipio)',
                'municipio': r'Municipio o delegación:\s*([A-ZÁÉÍÓÚÑ\s]+?)(?:\n|Colonia)',
                'colonia': r'Colonia:\s*([A-ZÁÉÍÓÚÑ0-9\s]+?)(?:\n|Tipo)',
                'tipo_vialidad': r'Tipo de vialidad:\s*([A-Z]+)',
                'nombre_vialidad': r'Nombre de la vialidad:\s*([A-ZÁÉÍÓÚÑ0-9\s]+?)(?:\n|Número)',
                'numero_exterior': r'Número exterior:\s*([0-9A-Z\s]*?)(?:\n|Número interior)',
                'numero_interior': r'Número interior:\s*([0-9A-Z\s]*?)(?:\n|CP)',
                'codigo_postal': r'CP:\s*(\d{5})',
                'correo_electronico': r'Correo electrónico:\s*([A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,})',
                'administracion_local': r'AL:\s*([A-ZÁÉÍÓÚÑ0-9\s]+?)(?:\n|$)',
                'regimen_fiscal': r'Régimen:\s*([A-ZÁÉÍÓÚÑ\s]+?)(?:\n|Fecha de alta)',
                'fecha_alta_regimen': r'Fecha de alta:\s*(\d{2}-\d{2}-\d{4})'
            }
            
            for campo, patron in patterns.items():
                match = re.search(patron, page_text, re.IGNORECASE | re.MULTILINE)
                if match:
                    valor = match.group(1).strip()
                    if valor:  # Solo asignar si no está vacío
                        info[campo] = valor
            
            # Construir nombre completo
            partes_nombre = [info.get('nombre'), info.get('apellido_paterno'), info.get('apellido_materno')]
            info['nombre_completo'] = ' '.join([parte for parte in partes_nombre if parte])
            
        except Exception as e:
            info['error'] = str(e)
            print(f"Error procesando página: {e}")
        
        return info
    
    def consultar_url_sat(self, url: str) -> Dict:
        """
        Consulta una URL del SAT y extrae la información.
        
        Args:
            url: URL del código QR del SAT
            
        Returns:
            Diccionario con información extraída
        """
        print(f"Consultando: {url}")
        
        try:
            # Extraer información de la página
            info = self.extraer_informacion_pagina(url)
            
            if info['pagina_valida']:
                print(f"  ✓ Información extraída para RFC: {info.get('rfc', 'N/A')}")
                print(f"    Nombre: {info.get('nombre_completo', 'N/A')}")
                print(f"    Situación: {info.get('situacion_contribuyente', 'N/A')}")
            else:
                print(f"  ⚠ Página no válida o sin información")
            
            return info
            
        except Exception as e:
            print(f"  ✗ Error general: {e}")
            return {
                'url_consultada': url,
                'timestamp_consulta': datetime.now().isoformat(),
                'pagina_valida': False,
                'error': f"Error general: {str(e)}"
            }
    
    def procesar_urls_desde_archivo(self, archivo_qr: str) -> List[Dict]:
        """
        Procesa URLs desde un archivo de QR extraídos.
        
        Args:
            archivo_qr: Ruta al archivo con URLs de QR
            
        Returns:
            Lista de diccionarios con información procesada
        """
        urls_encontradas = []
        
        try:
            with open(archivo_qr, 'r', encoding='utf-8') as f:
                contenido = f.read()
            
            # Buscar URLs en el contenido
            patron_url = r'(https://siat\.sat\.gob\.mx/app/qr/faces/pages/mobile/validadorqr\.jsf[^\s]+)'
            urls = re.findall(patron_url, contenido)
            
            print(f"Encontradas {len(urls)} URLs para procesar")
            
            # Inicializar navegador
            if not self.inicializar_navegador():
                print("Error: No se pudo inicializar el navegador")
                return []
            
            try:
                for i, url in enumerate(urls, 1):
                    print(f"\nProcesando {i}/{len(urls)}")
                    
                    # Consultar URL
                    info = self.consultar_url_sat(url)
                    urls_encontradas.append(info)
                    
                    # Delay entre requests para no sobrecargar el servidor
                    if i < len(urls):
                        print(f"  Esperando {self.delay_entre_requests} segundos...")
                        time.sleep(self.delay_entre_requests)
                        
            finally:
                # Cerrar navegador
                self.cerrar_navegador()
            
        except Exception as e:
            print(f"Error procesando archivo: {e}")
        
        return urls_encontradas

    def generar_csv_excel(self, datos: List[Dict], archivo_salida: str = "validacion_sat_playwright.csv"):
        """
        Genera un archivo CSV que se puede abrir en Excel.

        Args:
            datos: Lista de diccionarios con información extraída
            archivo_salida: Nombre del archivo CSV de salida
        """
        try:
            if not datos:
                print("No hay datos para generar CSV")
                return

            # Definir columnas en orden lógico
            columnas = [
                'rfc', 'nombre_completo', 'nombre', 'apellido_paterno', 'apellido_materno',
                'curp', 'fecha_nacimiento', 'situacion_contribuyente',
                'fecha_inicio_operaciones', 'fecha_ultimo_cambio_situacion',
                'entidad_federativa', 'municipio', 'colonia', 'codigo_postal',
                'tipo_vialidad', 'nombre_vialidad', 'numero_exterior', 'numero_interior',
                'correo_electronico', 'administracion_local',
                'regimen_fiscal', 'fecha_alta_regimen',
                'pagina_valida', 'url_consultada', 'timestamp_consulta', 'error'
            ]

            with open(archivo_salida, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=columnas)
                writer.writeheader()

                for dato in datos:
                    # Crear fila con todas las columnas, usando valores por defecto si no existen
                    fila = {}
                    for columna in columnas:
                        fila[columna] = dato.get(columna, '')
                    writer.writerow(fila)

            print(f"✓ Archivo CSV generado: {archivo_salida}")
            print(f"  Puedes abrirlo en Excel para ver los datos en formato tabla")

        except Exception as e:
            print(f"✗ Error generando CSV: {e}")

    def generar_csv_resumen(self, datos: List[Dict], archivo_salida: str = "resumen_sat_playwright.csv"):
        """
        Genera un CSV con solo los datos más importantes.

        Args:
            datos: Lista de diccionarios con información extraída
            archivo_salida: Nombre del archivo CSV de salida
        """
        try:
            # Filtrar solo datos válidos
            datos_validos = [d for d in datos if d.get('pagina_valida')]

            if not datos_validos:
                print("No hay datos válidos para el resumen")
                return

            # Columnas del resumen
            columnas_resumen = [
                'rfc', 'nombre_completo', 'situacion_contribuyente',
                'entidad_federativa', 'municipio', 'codigo_postal',
                'regimen_fiscal', 'correo_electronico', 'fecha_nacimiento'
            ]

            with open(archivo_salida, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=columnas_resumen)
                writer.writeheader()

                for dato in datos_validos:
                    fila = {}
                    for columna in columnas_resumen:
                        fila[columna] = dato.get(columna, '')
                    writer.writerow(fila)

            print(f"✓ Archivo CSV resumen generado: {archivo_salida}")

        except Exception as e:
            print(f"✗ Error generando CSV resumen: {e}")

    def generar_reporte_texto(self, datos: List[Dict], archivo_salida: str = "reporte_validacion_sat_playwright.txt"):
        """
        Genera un reporte de texto legible con la información extraída.

        Args:
            datos: Lista de diccionarios con información extraída
            archivo_salida: Nombre del archivo de texto de salida
        """
        try:
            with open(archivo_salida, 'w', encoding='utf-8') as f:
                f.write("REPORTE DE VALIDACIÓN DE CONSTANCIAS SAT (PLAYWRIGHT)\n")
                f.write("=" * 60 + "\n\n")
                f.write(f"Fecha de procesamiento: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Total de URLs procesadas: {len(datos)}\n\n")

                # Estadísticas
                datos_validos = [d for d in datos if d.get('pagina_valida')]
                f.write("ESTADÍSTICAS:\n")
                f.write("-" * 20 + "\n")
                f.write(f"URLs válidas: {len(datos_validos)}\n")
                f.write(f"URLs con error: {len(datos) - len(datos_validos)}\n")

                if datos_validos:
                    activos = sum(1 for d in datos_validos if d.get('situacion_contribuyente') == 'ACTIVO')
                    estados = set(d.get('entidad_federativa') for d in datos_validos if d.get('entidad_federativa'))
                    regimenes = set(d.get('regimen_fiscal') for d in datos_validos if d.get('regimen_fiscal'))

                    f.write(f"Contribuyentes activos: {activos}\n")
                    f.write(f"Estados únicos: {len(estados)}\n")
                    f.write(f"Regímenes fiscales únicos: {len(regimenes)}\n")

                f.write("\n")

                # Detalle por contribuyente
                f.write("DETALLE POR CONTRIBUYENTE:\n")
                f.write("-" * 40 + "\n\n")

                for i, dato in enumerate(datos, 1):
                    f.write(f"{i}. RFC: {dato.get('rfc', 'N/A')}\n")

                    if dato.get('pagina_valida'):
                        f.write(f"   Nombre: {dato.get('nombre_completo', 'N/A')}\n")
                        f.write(f"   CURP: {dato.get('curp', 'N/A')}\n")
                        f.write(f"   Situación: {dato.get('situacion_contribuyente', 'N/A')}\n")
                        f.write(f"   Fecha nacimiento: {dato.get('fecha_nacimiento', 'N/A')}\n")
                        f.write(f"   Estado: {dato.get('entidad_federativa', 'N/A')}\n")
                        f.write(f"   Municipio: {dato.get('municipio', 'N/A')}\n")
                        f.write(f"   CP: {dato.get('codigo_postal', 'N/A')}\n")
                        f.write(f"   Régimen: {dato.get('regimen_fiscal', 'N/A')}\n")
                        f.write(f"   Email: {dato.get('correo_electronico', 'N/A')}\n")
                    else:
                        f.write(f"   ERROR: {dato.get('error', 'Página no válida')}\n")

                    f.write(f"   URL: {dato.get('url_consultada', 'N/A')}\n")
                    f.write("\n")

            print(f"✓ Reporte de texto generado: {archivo_salida}")

        except Exception as e:
            print(f"✗ Error generando reporte: {e}")


def main():
    """Función principal."""
    print("=" * 60)
    print("    VALIDADOR WEB DE CONSTANCIAS SAT (PLAYWRIGHT)")
    print("=" * 60)
    print()

    # Buscar archivo de QR extraídos
    archivos_qr = [
        'qr_extraidos_opencv.txt',
        'qr_extraidos_windows.txt',
        'qr_extraidos.txt'
    ]

    archivo_encontrado = None
    for archivo in archivos_qr:
        if os.path.exists(archivo):
            archivo_encontrado = archivo
            break

    if not archivo_encontrado:
        print("Error: No se encontró ningún archivo de QR extraídos.")
        print("Ejecuta primero el script de extracción de QR.")
        return

    print(f"Procesando URLs desde: {archivo_encontrado}")
    print("ADVERTENCIA: Este proceso puede tomar varios minutos debido a los delays entre requests.")
    print("Se respeta un delay de 3 segundos entre consultas para no sobrecargar el servidor del SAT.\n")

    # Crear validador
    validador = ValidadorSATPlaywright()

    # Procesar URLs
    datos_extraidos = validador.procesar_urls_desde_archivo(archivo_encontrado)

    if not datos_extraidos:
        print("No se pudieron procesar las URLs.")
        return

    print(f"\n" + "=" * 60)
    print("GENERANDO REPORTES")
    print("=" * 60)

    # Generar archivos de salida
    validador.generar_csv_excel(datos_extraidos)
    validador.generar_csv_resumen(datos_extraidos)
    validador.generar_reporte_texto(datos_extraidos)

    # Mostrar resumen final
    datos_validos = [d for d in datos_extraidos if d.get('pagina_valida')]

    print(f"\nRESUMEN FINAL:")
    print("-" * 30)
    print(f"URLs procesadas: {len(datos_extraidos)}")
    print(f"Información extraída exitosamente: {len(datos_validos)}")
    print(f"Errores: {len(datos_extraidos) - len(datos_validos)}")

    if datos_validos:
        activos = sum(1 for d in datos_validos if d.get('situacion_contribuyente') == 'ACTIVO')
        print(f"Contribuyentes activos: {activos}")

        print(f"\nArchivos generados:")
        print(f"- validacion_sat_playwright.csv (Excel completo)")
        print(f"- resumen_sat_playwright.csv (Resumen para Excel)")
        print(f"- reporte_validacion_sat_playwright.txt (Reporte legible)")


if __name__ == "__main__":
    main()
