.\" Copyright 1999-2011 Glyph & Cog, LLC
.TH pdffonts 1 "15 August 2011"
.SH NAME
pdffonts \- Portable Document Format (PDF) font analyzer (version
3.03)
.SH SYNOPSIS
.B pdffonts
[options]
.RI [ PDF-file ]
.SH DESCRIPTION
.B Pdffonts
lists the fonts used in a Portable Document Format (PDF) file along
with various information for each font.
.PP
If
.I PDF-file
is \'-', it reads the PDF file from stdin.
.PP
The following information is listed for each font:
.TP
.B name
the font name, exactly as given in the PDF file (potentially including
a subset prefix)
.TP
.B type
the font type \(en see below for details
.TP
.B encoding
the font encoding
.TP
.B emb
"yes" if the font is embedded in the PDF file
.TP
.B sub
"yes" if the font is a subset
.TP
.B uni
"yes" if there is an explicit "ToUnicode" map in the PDF file (the
absence of a ToUnicode map doesn't necessarily mean that the text
can't be converted to Unicode)
.TP
.B object ID
the font dictionary object ID (number and generation)
.PP
PDF files can contain the following types of fonts:
.PP
.RS
Type 1
.RE
.RS
Type 1C \(en aka Compact Font Format (CFF)
.RE
.RS
Type 3
.RE
.RS
TrueType
.RE
.RS
CID Type 0 \(en 16-bit font with no specified type
.RE
.RS
CID Type 0C \(en 16-bit PostScript CFF font
.RE
.RS
CID TrueType \(en 16-bit TrueType font
.RE
.SH OPTIONS
.TP
.BI \-f " number"
Specifies the first page to analyze.
.TP
.BI \-l " number"
Specifies the last page to analyze.
.TP
.B \-subst
List the substitute fonts that poppler will use for non embedded fonts.
.TP
.BI \-opw " password"
Specify the owner password for the PDF file.  Providing this will
bypass all security restrictions.
.TP
.BI \-upw " password"
Specify the user password for the PDF file.
.TP
.B \-v
Print copyright and version information.
.TP
.B \-h
Print usage information.
.RB ( \-help
and
.B \-\-help
are equivalent.)
.SH EXIT CODES
The Xpdf tools use the following exit codes:
.TP
0
No error.
.TP
1
Error opening a PDF file.
.TP
2
Error opening an output file.
.TP
3
Error related to PDF permissions.
.TP
99
Other error.
.SH AUTHOR
The pdffonts software and documentation are copyright 1996\(en2011 Glyph
& Cog, LLC.
.SH "SEE ALSO"
.nh
.ad l
.BR pdfdetach (1),
.BR pdfimages (1),
.BR pdfinfo (1),
.BR pdftocairo (1),
.BR pdftohtml (1),
.BR pdftoppm (1),
.BR pdftops (1),
.BR pdftotext (1),
.BR pdfseparate (1),
.BR pdfsig (1),
.BR pdfunite (1)
